{"sysTime":"2025-07-02 11:45:53.116","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"background-preinit","className":"o.h.validator.internal.util.Version:21","methodName":"o.h.validator.internal.util.Version:<clinit>-21","message":"HV000001: Hibernate Validator 8.0.1.Final","thrown":""}
{"sysTime":"2025-07-02 11:45:53.110","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-07-02 11:45:53.178","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-07-02 11:45:53.445","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-07-02 11:45:53.473","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:88","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-88","message":"[settings] [req-serv] nacos-server port:8848","thrown":""}
{"sysTime":"2025-07-02 11:45:53.474","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:99","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-99","message":"[settings] [http-client] connect timeout:1000","thrown":""}
{"sysTime":"2025-07-02 11:45:53.474","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:106","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-106","message":"PER_TASK_CONFIG_SIZE: 3000.0","thrown":""}
{"sysTime":"2025-07-02 11:45:53.491","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"Thread-1","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-07-02 11:45:53.497","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-07-02 11:45:53.635","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-07-02 11:45:53.673","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.n.c.c.impl.LocalConfigInfoProcessor:212","methodName":"c.a.n.c.c.impl.LocalConfigInfoProcessor:<clinit>-212","message":"LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config","thrown":""}
{"sysTime":"2025-07-02 11:45:53.694","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.nacos.client.config.impl.Limiter:54","methodName":"c.a.nacos.client.config.impl.Limiter:<clinit>-54","message":"limitTime:5.0","thrown":""}
{"sysTime":"2025-07-02 11:45:53.997","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.nacos.client.config.utils.JvmUtil:49","methodName":"c.a.nacos.client.config.utils.JvmUtil:<clinit>-49","message":"isMultiInstance:false","thrown":""}
{"sysTime":"2025-07-02 11:45:54.270","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.c.n.c.NacosPropertySourceBuilder:97","methodName":"c.a.c.n.c.NacosPropertySourceBuilder:loadNacosData-97","message":"{\"msg\":\"Ignore the empty nacos configuration and get it based on dataId[delivery-application] & group[apply]\"}","thrown":""}
{"sysTime":"2025-07-02 11:45:54.342","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.c.n.c.NacosPropertySourceBuilder:97","methodName":"c.a.c.n.c.NacosPropertySourceBuilder:loadNacosData-97","message":"{\"msg\":\"Ignore the empty nacos configuration and get it based on dataId[delivery-application-dev.yaml] & group[apply]\"}","thrown":""}
{"sysTime":"2025-07-02 11:45:54.343","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.c.b.c.PropertySourceBootstrapConfiguration:133","methodName":"o.s.c.b.c.PropertySourceBootstrapConfiguration:doInitialize-133","message":"Located property source: [BootstrapPropertySource {name='bootstrapProperties-delivery-application-dev.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-application.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-application,apply'}, BootstrapPropertySource {name='bootstrapProperties-apply-cos-tencent.properties,apply'}, BootstrapPropertySource {name='bootstrapProperties-jd-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-kd100-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-yunda-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-common-starter-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-mysql.yaml,apply'}]","thrown":""}
{"sysTime":"2025-07-02 11:45:54.348","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-07-02 11:45:54.349","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.e.d.application.DeliveryApplication:660","methodName":"c.e.d.application.DeliveryApplication:logStartupProfileInfo-660","message":"The following 1 profile is active: \"dev\"","thrown":""}
{"sysTime":"2025-07-02 11:45:55.088","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:292","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:multipleStoresDetected-292","message":"Multiple Spring Data modules found, entering strict repository configuration mode","thrown":""}
{"sysTime":"2025-07-02 11:45:55.089","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:139","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:registerRepositoriesIn-139","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","thrown":""}
{"sysTime":"2025-07-02 11:45:55.113","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:208","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:registerRepositoriesIn-208","message":"Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.","thrown":""}
{"sysTime":"2025-07-02 11:45:55.322","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.cloud.context.scope.GenericScope:282","methodName":"o.s.cloud.context.scope.GenericScope:setSerializationId-282","message":"BeanFactory id=42461a83-c613-35d1-8cfa-e9865c6c8765","thrown":""}
{"sysTime":"2025-07-02 11:45:55.538","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-07-02 11:45:55.541","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-07-02 11:45:55.543","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$836/0x00000070017014c0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-07-02 11:45:55.546","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-07-02 11:45:55.563","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-07-02 11:45:55.569","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-07-02 11:45:55.575","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.jakarta.DsJakartaHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-07-02 11:45:55.595","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:429","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-429","message":"{\"msg\":\"Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.\"}","thrown":""}
{"sysTime":"2025-07-02 11:45:55.598","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-07-02 11:45:55.798","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.b.w.embedded.tomcat.TomcatWebServer:109","methodName":"o.s.b.w.embedded.tomcat.TomcatWebServer:initialize-109","message":"Tomcat initialized with port 20130 (http)","thrown":""}
{"sysTime":"2025-07-02 11:45:55.804","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.apache.coyote.http11.Http11NioProtocol:173","methodName":"o.apache.coyote.http11.Http11NioProtocol:log-173","message":"Initializing ProtocolHandler [\"http-nio-20130\"]","thrown":""}
{"sysTime":"2025-07-02 11:45:55.808","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.apache.catalina.core.StandardService:173","methodName":"o.apache.catalina.core.StandardService:log-173","message":"Starting service [Tomcat]","thrown":""}
{"sysTime":"2025-07-02 11:45:55.808","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"org.apache.catalina.core.StandardEngine:173","methodName":"org.apache.catalina.core.StandardEngine:log-173","message":"Starting Servlet engine: [Apache Tomcat/10.1.31]","thrown":""}
{"sysTime":"2025-07-02 11:45:55.847","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.a.c.c.C.[Tomcat].[localhost].[/]:173","methodName":"o.a.c.c.C.[Tomcat].[localhost].[/]:log-173","message":"Initializing Spring embedded WebApplicationContext","thrown":""}
{"sysTime":"2025-07-02 11:45:55.847","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.b.w.s.c.ServletWebServerApplicationContext:296","methodName":"o.s.b.w.s.c.ServletWebServerApplicationContext:prepareWebApplicationContext-296","message":"Root WebApplicationContext: initialization completed in 1488 ms","thrown":""}
{"sysTime":"2025-07-02 11:45:57.884","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-1,db-issuer-admin} inited","thrown":""}
{"sysTime":"2025-07-02 11:45:59.655","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-2,db-issuer-admin-proxy} inited","thrown":""}
{"sysTime":"2025-07-02 11:46:01.669","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-3,db-issuer-admin-minor} inited","thrown":""}
{"sysTime":"2025-07-02 11:46:03.914","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-4,db-service} inited","thrown":""}
{"sysTime":"2025-07-02 11:46:03.918","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin-proxy] success","thrown":""}
{"sysTime":"2025-07-02 11:46:03.919","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin] success","thrown":""}
{"sysTime":"2025-07-02 11:46:03.919","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-service] success","thrown":""}
{"sysTime":"2025-07-02 11:46:03.919","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin-minor] success","thrown":""}
{"sysTime":"2025-07-02 11:46:03.920","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:234","methodName":"c.b.d.d.DynamicRoutingDataSource:afterPropertiesSet-234","message":"dynamic-datasource initial loaded [4] datasource,primary datasource named [db-issuer-admin]","thrown":""}
{"sysTime":"2025-07-02 11:46:04.714","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-event-delivery defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:04.779","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:06.092","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"org.redisson.Version:43","methodName":"org.redisson.Version:logVersion-43","message":"Redisson 3.38.1","thrown":""}
{"sysTime":"2025-07-02 11:46:06.275","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"redisson-netty-2-6","className":"o.redisson.connection.ConnectionsHolder:132","methodName":"o.redisson.connection.ConnectionsHolder:lambda$initConnections$1-132","message":"1 connections initialized for ************/************:6379","thrown":""}
{"sysTime":"2025-07-02 11:46:07.845","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"redisson-netty-2-19","className":"o.redisson.connection.ConnectionsHolder:132","methodName":"o.redisson.connection.ConnectionsHolder:lambda$initConnections$1-132","message":"24 connections initialized for ************/************:6379","thrown":""}
{"sysTime":"2025-07-02 11:46:08.285","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-email-delivery defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:08.287","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:08.324","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.alibaba.nacos.client.naming:65","methodName":"com.alibaba.nacos.client.naming:call-65","message":"initializer namespace from System Property :null","thrown":""}
{"sysTime":"2025-07-02 11:46:08.325","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.alibaba.nacos.client.naming:74","methodName":"com.alibaba.nacos.client.naming:call-74","message":"initializer namespace from System Environment :null","thrown":""}
{"sysTime":"2025-07-02 11:46:08.325","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.alibaba.nacos.client.naming:84","methodName":"com.alibaba.nacos.client.naming:call-84","message":"initializer namespace from System Property :null","thrown":""}
{"sysTime":"2025-07-02 11:46:08.331","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.e.deploy.NacosDynamicServerListWatcher:37","methodName":"c.e.deploy.NacosDynamicServerListWatcher:startWatch-37","message":"启动nacos服务变更监听: ","thrown":""}
{"sysTime":"2025-07-02 11:46:08.425","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.alibaba.nacos.client.naming:242","methodName":"com.alibaba.nacos.client.naming:processServiceJson-242","message":"new ips(1) service: apply@@delivery-application -> [{\"instanceId\":\"*************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"*************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-07-02 11:46:08.429","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.alibaba.nacos.client.naming:281","methodName":"com.alibaba.nacos.client.naming:processServiceJson-281","message":"current ips:(1) service: apply@@delivery-application -> [{\"instanceId\":\"*************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"*************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-07-02 11:46:08.646","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_express_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:08.648","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:08.651","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:08.897","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_express_group, nameServerAddr=name-service:9876, topic=ets_express_topic, tag=queueExpress","thrown":""}
{"sysTime":"2025-07-02 11:46:08.908","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_review_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:08.910","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:08.913","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:09.113","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_review_group, nameServerAddr=name-service:9876, topic=ets_review_topic, tag=queueReview","thrown":""}
{"sysTime":"2025-07-02 11:46:09.123","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_java_delivery_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:09.127","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:09.130","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:09.327","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_java_delivery_group, nameServerAddr=name-service:9876, topic=ets_java_delivery_task_topic, tag=queueTask","thrown":""}
{"sysTime":"2025-07-02 11:46:09.337","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent","className":"c.e.deploy.NacosDynamicServerListWatcher:42","methodName":"c.e.deploy.NacosDynamicServerListWatcher:lambda$startWatch$0-42","message":"监听到nacos服务变更：serviceName: apply@@delivery-application, groupName：apply, 实例：[Instance{instanceId='*************#20130#DEFAULT#apply@@delivery-application', ip='*************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}]","thrown":""}
{"sysTime":"2025-07-02 11:46:09.495","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:09.670","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets-group-event-delivery, nameServerAddr=name-service:9876, topic=ETS_EVENT, tag=queueEvent","thrown":""}
{"sysTime":"2025-07-02 11:46:09.682","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_java_risk_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:09.684","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:09.686","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-07-02 11:46:09.901","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_java_risk_group, nameServerAddr=name-service:9876, topic=ets_java_risk_task_topic, tag=queueRiskTask","thrown":""}
{"sysTime":"2025-07-02 11:46:10.581","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:94","methodName":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:afterPropertiesSet-94","message":"{\"msg\":\"Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.\"}","thrown":""}
{"sysTime":"2025-07-02 11:46:10.595","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.b.a.e.web.EndpointLinksResolver:58","methodName":"o.s.b.a.e.web.EndpointLinksResolver:<init>-58","message":"Exposing 21 endpoint(s) beneath base path '/actuator'","thrown":""}
{"sysTime":"2025-07-02 11:46:10.713","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.apache.coyote.http11.Http11NioProtocol:173","methodName":"o.apache.coyote.http11.Http11NioProtocol:log-173","message":"Starting ProtocolHandler [\"http-nio-20130\"]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.717","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.b.w.embedded.tomcat.TomcatWebServer:241","methodName":"o.s.b.w.embedded.tomcat.TomcatWebServer:start-241","message":"Tomcat started on port 20130 (http) with context path ''","thrown":""}
{"sysTime":"2025-07-02 11:46:10.718","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.alibaba.nacos.client.naming:81","methodName":"com.alibaba.nacos.client.naming:addBeatInfo-81","message":"[BEAT] adding beat: BeatInfo{port=20130, ip='************', weight=1.0, serviceName='apply@@delivery-application', cluster='DEFAULT', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.","thrown":""}
{"sysTime":"2025-07-02 11:46:10.718","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"com.alibaba.nacos.client.naming:238","methodName":"com.alibaba.nacos.client.naming:registerService-238","message":"[REGISTER-SERVICE] ets-dev registering service apply@@delivery-application with instance: Instance{instanceId='null', ip='************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}","thrown":""}
{"sysTime":"2025-07-02 11:46:10.753","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.c.n.registry.NacosServiceRegistry:76","methodName":"c.a.c.n.registry.NacosServiceRegistry:register-76","message":"nacos registry, apply delivery-application ************:20130 register finished","thrown":""}
{"sysTime":"2025-07-02 11:46:10.798","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.e.d.application.DeliveryApplication:56","methodName":"c.e.d.application.DeliveryApplication:logStarted-56","message":"Started DeliveryApplication in 18.145 seconds (process running for 18.634)","thrown":""}
{"sysTime":"2025-07-02 11:46:10.803","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] common-starter-config.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-07-02 11:46:10.803","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=common-starter-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-07-02 11:46:10.803","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=common-starter-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-07-02 11:46:10.803","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] delivery-application-dev.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-07-02 11:46:10.803","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=delivery-application-dev.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-07-02 11:46:10.803","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-application-dev.yaml, group=apply","thrown":""}
{"sysTime":"2025-07-02 11:46:10.804","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] jd-config.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-07-02 11:46:10.804","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=jd-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-07-02 11:46:10.804","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=jd-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-07-02 11:46:10.804","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] delivery-application+apply+ets-dev","thrown":""}
{"sysTime":"2025-07-02 11:46:10.804","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=delivery-application, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-07-02 11:46:10.804","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-application, group=apply","thrown":""}
{"sysTime":"2025-07-02 11:46:10.804","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] delivery-config.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-07-02 11:46:10.805","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=delivery-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-07-02 11:46:10.805","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-07-02 11:46:10.805","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] yunda-config.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-07-02 11:46:10.805","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=yunda-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-07-02 11:46:10.805","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=yunda-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-07-02 11:46:10.805","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] kd100-config.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-07-02 11:46:10.805","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=kd100-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-07-02 11:46:10.805","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=kd100-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-07-02 11:46:10.805","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] delivery-application.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-07-02 11:46:10.805","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=delivery-application.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-07-02 11:46:10.806","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-application.yaml, group=apply","thrown":""}
{"sysTime":"2025-07-02 11:46:10.806","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] delivery-mysql.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-07-02 11:46:10.806","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=delivery-mysql.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-07-02 11:46:10.806","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-mysql.yaml, group=apply","thrown":""}
{"sysTime":"2025-07-02 11:46:10.813","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:retryAfterSalesReviewsNotifyHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4f6f13f0[class com.ets.delivery.application.app.job.AfterSalesReviewsNotifyJob$$SpringCGLIB$$0#retryAfterSalesReviewsNotifyHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.814","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:manualNotifyAfterSalesReviewHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2e9ab0b3[class com.ets.delivery.application.app.job.AfterSalesReviewsNotifyJob$$SpringCGLIB$$0#manualNotifyAfterSalesReviewHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.814","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:erpOrderDailyCheckHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6b283550[class com.ets.delivery.application.app.job.ErpJob$$SpringCGLIB$$0#erpOrderDailyCheckHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.814","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:fixErpOrderHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2460c928[class com.ets.delivery.application.app.job.ErpJob$$SpringCGLIB$$0#fixErpOrderHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.814","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:expressSubscribe, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@13a7aec7[class com.ets.delivery.application.app.job.ExpressJob$$SpringCGLIB$$0#expressSubscribe]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.814","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:LogisticsQueryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@68c39b28[class com.ets.delivery.application.app.job.LogisticsQueryJob$$SpringCGLIB$$0#logisticsQueryHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.814","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:yundaLogisticsExpressQueryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@426ba1d5[class com.ets.delivery.application.app.job.LogisticsQueryJob$$SpringCGLIB$$0#yundaLogisticsExpressQueryHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.814","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:fixLogisticsExpressStatusHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@60600939[class com.ets.delivery.application.app.job.LogisticsQueryJob$$SpringCGLIB$$0#fixLogisticsExpressStatusHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.814","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:pickUpAutoCancelHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4e9f4264[class com.ets.delivery.application.app.job.PickUpJob$$SpringCGLIB$$0#pickUpAutoCancelHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.815","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:pickUpQueryTraceInfoHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3af18a44[class com.ets.delivery.application.app.job.PickUpJob$$SpringCGLIB$$0#pickUpQueryTraceInfoHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.815","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:postReviewReleaseHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@258be1c6[class com.ets.delivery.application.app.job.PostReviewJob$$SpringCGLIB$$0#postReviewReleaseHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.815","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:initPostReviewDataHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@45c20d8f[class com.ets.delivery.application.app.job.PostReviewJob$$SpringCGLIB$$0#initPostReviewDataHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.815","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reviewDateSummaryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@71faeb31[class com.ets.delivery.application.app.job.PostReviewSummaryJob$$SpringCGLIB$$0#reviewDateSummaryHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.815","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reviewUserSummaryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@47effce0[class com.ets.delivery.application.app.job.PostReviewSummaryJob$$SpringCGLIB$$0#reviewUserSummaryHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.815","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:applyReviewOrderRiskHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3d10a00a[class com.ets.delivery.application.app.job.RiskReviewJob$$SpringCGLIB$$0#applyReviewOrderRiskHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.815","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:releaseUserRiskReviewHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@68eb7c52[class com.ets.delivery.application.app.job.RiskReviewJob$$SpringCGLIB$$0#releaseUserRiskReviewHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.816","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:checkOvertimeHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4e6eb11d[class com.ets.delivery.application.app.job.SendBackJob$$SpringCGLIB$$0#checkOvertimeHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.816","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:logisticsAvgHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@765e4ccc[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#logisticsAvgHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.816","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:goodsStockHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4ef6a2fd[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#goodsStockHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.816","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:storageAlarmFileHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7dcc5e9d[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#storageAlarmFileHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.816","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:stockOutSkuHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4611fe60[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#stockOutSkuHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.816","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:storageMapAddressCheckHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@42adf242[class com.ets.delivery.application.app.job.StorageMapJob$$SpringCGLIB$$0#storageMa1pAddressCheck]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.816","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reExecHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@393751a0[class com.ets.delivery.application.app.job.TaskJob$$SpringCGLIB$$0#reExecHandler]","thrown":""}
{"sysTime":"2025-07-02 11:46:10.951","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"o.s.c.openfeign.FeignClientFactoryBean:468","methodName":"o.s.c.openfeign.FeignClientFactoryBean:getTarget-468","message":"For 'base-application' URL not provided. Will try picking an instance via load-balancing.","thrown":""}
{"sysTime":"2025-07-02 11:46:11.114","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"main","className":"c.x.r.r.provider.XxlRpcProviderFactory:197","methodName":"c.x.r.r.provider.XxlRpcProviderFactory:addService-197","message":">>>>>>>>>>> xxl-rpc, provider factory add service success. serviceKey = com.xxl.job.core.biz.ExecutorBiz, serviceBean = class com.xxl.job.core.biz.impl.ExecutorBizImpl","thrown":""}
{"sysTime":"2025-07-02 11:46:11.121","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"Thread-35","className":"com.xxl.rpc.remoting.net.Server:66","methodName":"com.xxl.rpc.remoting.net.Server:run-66","message":">>>>>>>>>>> xxl-rpc remoting server start success, nettype = com.xxl.rpc.remoting.net.impl.netty_http.server.NettyHttpServer, port = 20132","thrown":""}
{"sysTime":"2025-07-02 11:46:11.201","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"RMI TCP Connection(14)-127.0.0.1","className":"o.a.c.c.C.[Tomcat].[localhost].[/]:173","methodName":"o.a.c.c.C.[Tomcat].[localhost].[/]:log-173","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'","thrown":""}
{"sysTime":"2025-07-02 11:46:11.201","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"RMI TCP Connection(14)-127.0.0.1","className":"o.s.web.servlet.DispatcherServlet:532","methodName":"o.s.web.servlet.DispatcherServlet:initServletBean-532","message":"Initializing Servlet 'dispatcherServlet'","thrown":""}
{"sysTime":"2025-07-02 11:46:11.203","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"RMI TCP Connection(14)-127.0.0.1","className":"o.s.web.servlet.DispatcherServlet:554","methodName":"o.s.web.servlet.DispatcherServlet:initServletBean-554","message":"Completed initialization in 1 ms","thrown":""}
{"sysTime":"2025-07-02 11:46:19.512","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:242","methodName":"com.alibaba.nacos.client.naming:processServiceJson-242","message":"new ips(1) service: apply@@delivery-application -> [{\"instanceId\":\"************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-07-02 11:46:19.515","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent","className":"c.e.deploy.NacosDynamicServerListWatcher:42","methodName":"c.e.deploy.NacosDynamicServerListWatcher:lambda$startWatch$0-42","message":"监听到nacos服务变更：serviceName: apply@@delivery-application, groupName：apply, 实例：[Instance{instanceId='************#20130#DEFAULT#apply@@delivery-application', ip='************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}, Instance{instanceId='*************#20130#DEFAULT#apply@@delivery-application', ip='*************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}]","thrown":""}
{"sysTime":"2025-07-02 11:46:19.516","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:281","methodName":"com.alibaba.nacos.client.naming:processServiceJson-281","message":"current ips:(2) service: apply@@delivery-application -> [{\"instanceId\":\"************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000},{\"instanceId\":\"*************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"*************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-07-02 11:46:43.190","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"6797dec6-bde2-4a0d-bb99-095ee0a6715f","spanId":"0","parentId":"","exportable":"","pid":"33621","thread":"http-nio-20130-exec-1","className":"c.e.s.interceptor.BeforeInterceptor:61","methodName":"c.e.s.interceptor.BeforeInterceptor:preHandle-61","message":"{\"header\":{\"referer\":\"https://gdmg-dev.etczs.net/user/product\",\"content-length\":\"0\",\"host\":\"127.0.0.1:20130\",\"appcode\":\"UnifinedPlatform\",\"logincode\":\"huanghaojie\",\"connection\":\"keep-alive\",\"accept-encoding\":\"gzip, deflate, br\",\"token\":\"972b876c55d643ffc489eb08fb7ae454\",\"user-agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"accept\":\"*/*\"},\"requestUri\":\"/admin/risk-reviews/get-recheck\",\"params\":{}}","thrown":""}
{"sysTime":"2025-07-02 11:46:43.578","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"6797dec6-bde2-4a0d-bb99-095ee0a6715f","spanId":"0","parentId":"","exportable":"","pid":"33621","thread":"http-nio-20130-exec-1","className":"c.e.s.filter.HttpServletResponseFilter:44","methodName":"c.e.s.filter.HttpServletResponseFilter:doFilter-44","message":"{\"code\":-1,\"msg\":\"风控审核单不存在\",\"data\":null}","thrown":""}
{"sysTime":"2025-07-02 11:46:50.912","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"2e86ef38-09b3-4761-acfd-d88edd5854d8","spanId":"0","parentId":"","exportable":"","pid":"33621","thread":"http-nio-20130-exec-2","className":"c.e.s.interceptor.BeforeInterceptor:61","methodName":"c.e.s.interceptor.BeforeInterceptor:preHandle-61","message":"{\"header\":{\"referer\":\"https://gdmg-dev.etczs.net/user/product\",\"content-length\":\"0\",\"host\":\"127.0.0.1:20130\",\"appcode\":\"UnifinedPlatform\",\"logincode\":\"huanghaojie\",\"connection\":\"keep-alive\",\"accept-encoding\":\"gzip, deflate, br\",\"token\":\"972b876c55d643ffc489eb08fb7ae454\",\"user-agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"accept\":\"*/*\"},\"requestUri\":\"/admin/risk-reviews/get-recheck\",\"params\":{}}","thrown":""}
{"sysTime":"2025-07-02 11:47:41.473","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"2e86ef38-09b3-4761-acfd-d88edd5854d8","spanId":"0","parentId":"","exportable":"","pid":"33621","thread":"http-nio-20130-exec-2","className":"c.e.s.filter.HttpServletResponseFilter:44","methodName":"c.e.s.filter.HttpServletResponseFilter:doFilter-44","message":"{\"code\":-1,\"msg\":\"风控审核单不存在\",\"data\":null}","thrown":""}
{"sysTime":"2025-07-02 11:47:41.492","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:238","methodName":"com.alibaba.nacos.client.naming:registerService-238","message":"[REGISTER-SERVICE] ets-dev registering service apply@@delivery-application with instance: Instance{instanceId='null', ip='************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}","thrown":""}
{"sysTime":"2025-07-02 11:50:30.188","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"6fc96a7d-7acd-4976-bede-ed6505afb5f0","spanId":"0","parentId":"","exportable":"","pid":"33621","thread":"http-nio-20130-exec-4","className":"c.e.s.interceptor.BeforeInterceptor:61","methodName":"c.e.s.interceptor.BeforeInterceptor:preHandle-61","message":"{\"header\":{\"referer\":\"https://gdmg-dev.etczs.net/user/product\",\"content-length\":\"0\",\"host\":\"127.0.0.1:20130\",\"appcode\":\"UnifinedPlatform\",\"logincode\":\"huanghaojie\",\"connection\":\"keep-alive\",\"accept-encoding\":\"gzip, deflate, br\",\"token\":\"972b876c55d643ffc489eb08fb7ae454\",\"user-agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"accept\":\"*/*\"},\"requestUri\":\"/admin/risk-reviews/get-recheck\",\"params\":{}}","thrown":""}
{"sysTime":"2025-07-02 11:50:34.980","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"6fc96a7d-7acd-4976-bede-ed6505afb5f0","spanId":"0","parentId":"","exportable":"","pid":"33621","thread":"http-nio-20130-exec-4","className":"c.e.s.filter.HttpServletResponseFilter:44","methodName":"c.e.s.filter.HttpServletResponseFilter:doFilter-44","message":"{\"code\":-1,\"msg\":\"风控审核单不存在\",\"data\":null}","thrown":""}
{"sysTime":"2025-07-02 13:34:38.160","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"f97ec0d4-3c6c-4457-9f5d-3c7b852c9647","spanId":"0","parentId":"","exportable":"","pid":"33621","thread":"http-nio-20130-exec-6","className":"c.e.s.interceptor.BeforeInterceptor:61","methodName":"c.e.s.interceptor.BeforeInterceptor:preHandle-61","message":"{\"header\":{\"referer\":\"https://gdmg-dev.etczs.net/user/product\",\"content-length\":\"0\",\"host\":\"127.0.0.1:20130\",\"appcode\":\"UnifinedPlatform\",\"logincode\":\"huanghaojie\",\"connection\":\"keep-alive\",\"accept-encoding\":\"gzip, deflate, br\",\"token\":\"972b876c55d643ffc489eb08fb7ae454\",\"user-agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"accept\":\"*/*\"},\"requestUri\":\"/admin/risk-reviews/get-recheck\",\"params\":{}}","thrown":""}
{"sysTime":"2025-07-02 13:34:38.819","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"f97ec0d4-3c6c-4457-9f5d-3c7b852c9647","spanId":"0","parentId":"","exportable":"","pid":"33621","thread":"http-nio-20130-exec-6","className":"c.e.d.a.app.business.RiskReviewBusiness:472","methodName":"c.e.d.a.app.business.RiskReviewBusiness:getOneRiskReviewByType-472","message":"{\"userId\":\"\",\"url\":\"/admin/risk-reviews/get-recheck\",\"msg\":\"获取风控审核单详情失败，风控单号：2506301848000000041，类型：2，错误：风控审核单不存在\"}","thrown":""}
{"sysTime":"2025-07-02 13:34:40.466","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"f97ec0d4-3c6c-4457-9f5d-3c7b852c9647","spanId":"0","parentId":"","exportable":"","pid":"33621","thread":"http-nio-20130-exec-6","className":"c.e.d.a.app.business.RiskReviewBusiness:524","methodName":"c.e.d.a.app.business.RiskReviewBusiness:getOneRiskReviewByType-524","message":"用户 huanghaojie 新领取风控审核单：2507021100000000161，类型：2","thrown":""}
{"sysTime":"2025-07-02 13:34:40.674","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"f97ec0d4-3c6c-4457-9f5d-3c7b852c9647","spanId":"0","parentId":"","exportable":"","pid":"33621","thread":"http-nio-20130-exec-6","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=CosFeign, name=baseCosFeign, url=http://base-application:20120/cos)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/getSignUrl\\\\\\\"}, produces={}, value={\\\\\\\"/getSignUrl\\\\\\\"})\\\"]\",\"parameterNames\":\"[\\\"dto\\\"]\",\"args\":\"[{\\\"seconds\\\":21600,\\\"urls\\\":{\\\"520\\\":\\\"http://dev-cos.etczs.net/idcard_back/20250630/a988d1e2b368b5fce0c5eed8b6db0bdb\\\",\\\"422\\\":\\\"http://dev-cos.etczs.net/idcard_front/20250630/05aa59c2c3c13bd91998081fec4159ef\\\",\\\"1054\\\":\\\"https://wecos.etczs.net/assets/images/default_car_front_1017-0.jpg\\\",\\\"1142\\\":\\\"http://dev-cos.etczs.net/idcard_hold/20250702/d27c3e0ceaf422e84fed496e3f5731d1\\\",\\\"736\\\":\\\"http://dev-cos.etczs.net/vehicle_front/20250630/65940a8d4a2ca2b234723fbe71827109\\\",\\\"836\\\":\\\"http://dev-cos.etczs.net/vehicle_back/20250630/fb9b0d98415ffec03b4f29a68d384f2c\\\"}}]\",\"result\":\"{\\\"code\\\":0,\\\"data\\\":{\\\"signedCount\\\":5,\\\"urls\\\":{\\\"520\\\":\\\"http://dev-cos.etczs.net/idcard_back/20250630/a988d1e2b368b5fce0c5eed8b6db0bdb?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLWp7jOpBPSMkzFMI6lHk7G8sRmtujYNA%26q-sign-time%3D1751434480%3B1751456080%26q-key-time%3D1751434480%3B1751456080%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%3Db0eec66032345ea7345a0b39b0b725e1156c7752\\\",\\\"422\\\":\\\"http://dev-cos.etczs.net/idcard_front/20250630/05aa59c2c3c13bd91998081fec4159ef?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLWp7jOpBPSMkzFMI6lHk7G8sRmtujYNA%26q-sign-time%3D1751434480%3B1751456080%26q-key-time%3D1751434480%3B1751456080%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%3D3ed31abcdd8b06545797fd55174833aefbbd0c28\\\",\\\"1054\\\":\\\"https://wecos.etczs.net/assets/images/default_car_front_1017-0.jpg\\\",\\\"1142\\\":\\\"http://dev-cos.etczs.net/idcard_hold/20250702/d27c3e0ceaf422e84fed496e3f5731d1?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLWp7jOpBPSMkzFMI6lHk7G8sRmtujYNA%26q-sign-time%3D1751434480%3B1751456080%26q-key-time%3D1751434480%3B1751456080%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%3D509e9b18f9e3eaaffd1952ff07fdaf6c7cdecd3b\\\",\\\"736\\\":\\\"http://dev-cos.etczs.net/vehicle_front/20250630/65940a8d4a2ca2b234723fbe71827109?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLWp7jOpBPSMkzFMI6lHk7G8sRmtujYNA%26q-sign-time%3D1751434480%3B1751456080%26q-key-time%3D1751434480%3B1751456080%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%3D03b56b4a13eea26325a5eb213df8c1464b7bcf3a\\\",\\\"836\\\":\\\"http://dev-cos.etczs.net/vehicle_back/20250630/fb9b0d98415ffec03b4f29a68d384f2c?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLWp7jOpBPSMkzFMI6lHk7G8sRmtujYNA%26q-sign-time%3D1751434480%3B1751456080%26q-key-time%3D1751434480%3B1751456080%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%3D00487f9f47136d330963d93fc7a943eaa52b94c5\\\"}},\\\"msg\\\":\\\"success\\\"}\",\"usedTime\":\"195ms\"}","thrown":""}
{"sysTime":"2025-07-02 13:34:40.680","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"f97ec0d4-3c6c-4457-9f5d-3c7b852c9647","spanId":"0","parentId":"","exportable":"","pid":"33621","thread":"http-nio-20130-exec-6","className":"c.e.s.filter.HttpServletResponseFilter:44","methodName":"c.e.s.filter.HttpServletResponseFilter:doFilter-44","message":"{\"code\":0,\"msg\":\"success\",\"data\":{\"id\":8,\"riskReviewSn\":\"2507021100000000161\",\"riskType\":2,\"riskTypeStr\":\"复审\",\"businessSn\":\"250630165900262201A1\",\"riskReviewStatus\":0,\"riskReviewStatusStr\":\"待审核\",\"rejectReasonId\":0,\"rejectReason\":\"\",\"previewRiskReviewSn\":\"2507012052000000151\",\"previewRejectReason\":\"请进行实名认证及车辆资料补传\",\"riskRuleRemark\":\"收货手机号存在超过两笔激活成功且未注销的订单;\",\"reviewInfo\":{\"reviewSn\":\"2507011826000002611\",\"idCardFrontUrl\":\"http://dev-cos.etczs.net/idcard_front/20250630/05aa59c2c3c13bd91998081fec4159ef?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLWp7jOpBPSMkzFMI6lHk7G8sRmtujYNA%26q-sign-time%3D1751434480%3B1751456080%26q-key-time%3D1751434480%3B1751456080%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%3D3ed31abcdd8b06545797fd55174833aefbbd0c28\",\"idCardBackUrl\":\"http://dev-cos.etczs.net/idcard_back/20250630/a988d1e2b368b5fce0c5eed8b6db0bdb?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLWp7jOpBPSMkzFMI6lHk7G8sRmtujYNA%26q-sign-time%3D1751434480%3B1751456080%26q-key-time%3D1751434480%3B...","thrown":""}
{"sysTime":"2025-07-02 13:35:18.263","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"699806f3-535a-47b4-a017-ae704181d9cd","spanId":"0","parentId":"","exportable":"","pid":"33621","thread":"http-nio-20130-exec-7","className":"c.e.s.interceptor.BeforeInterceptor:61","methodName":"c.e.s.interceptor.BeforeInterceptor:preHandle-61","message":"{\"header\":{\"referer\":\"https://gdmg-dev.etczs.net/user/product\",\"content-length\":\"0\",\"host\":\"127.0.0.1:20130\",\"appcode\":\"UnifinedPlatform\",\"logincode\":\"huanghaojie\",\"connection\":\"keep-alive\",\"accept-encoding\":\"gzip, deflate, br\",\"token\":\"972b876c55d643ffc489eb08fb7ae454\",\"user-agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"accept\":\"*/*\"},\"requestUri\":\"/admin/risk-reviews/get-recheck\",\"params\":{}}","thrown":""}
{"sysTime":"2025-07-02 13:35:18.538","level":"ERROR","ip":"127.0.0.1","appName":"delivery-application","traceId":"699806f3-535a-47b4-a017-ae704181d9cd","spanId":"0","parentId":"","exportable":"","pid":"33621","thread":"http-nio-20130-exec-7","className":"c.e.d.a.app.business.RiskReviewBusiness:472","methodName":"c.e.d.a.app.business.RiskReviewBusiness:getOneRiskReviewByType-472","message":"{\"userId\":\"\",\"url\":\"/admin/risk-reviews/get-recheck\",\"msg\":\"获取风控审核单详情失败，风控单号：2506301848000000041，类型：2，错误：风控审核单不存在\"}","thrown":""}
{"sysTime":"2025-07-02 13:35:19.174","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"699806f3-535a-47b4-a017-ae704181d9cd","spanId":"0","parentId":"","exportable":"","pid":"33621","thread":"http-nio-20130-exec-7","className":"c.e.d.a.app.business.RiskReviewBusiness:468","methodName":"c.e.d.a.app.business.RiskReviewBusiness:getOneRiskReviewByType-468","message":"用户 huanghaojie 获取已领取的风控审核单：2507021100000000161，类型：2","thrown":""}
{"sysTime":"2025-07-02 13:35:19.254","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"699806f3-535a-47b4-a017-ae704181d9cd","spanId":"0","parentId":"","exportable":"","pid":"33621","thread":"http-nio-20130-exec-7","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=CosFeign, name=baseCosFeign, url=http://base-application:20120/cos)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/getSignUrl\\\\\\\"}, produces={}, value={\\\\\\\"/getSignUrl\\\\\\\"})\\\"]\",\"parameterNames\":\"[\\\"dto\\\"]\",\"args\":\"[{\\\"seconds\\\":21600,\\\"urls\\\":{\\\"520\\\":\\\"http://dev-cos.etczs.net/idcard_back/20250630/a988d1e2b368b5fce0c5eed8b6db0bdb\\\",\\\"422\\\":\\\"http://dev-cos.etczs.net/idcard_front/20250630/05aa59c2c3c13bd91998081fec4159ef\\\",\\\"1054\\\":\\\"https://wecos.etczs.net/assets/images/default_car_front_1017-0.jpg\\\",\\\"1142\\\":\\\"http://dev-cos.etczs.net/idcard_hold/20250702/d27c3e0ceaf422e84fed496e3f5731d1\\\",\\\"736\\\":\\\"http://dev-cos.etczs.net/vehicle_front/20250630/65940a8d4a2ca2b234723fbe71827109\\\",\\\"836\\\":\\\"http://dev-cos.etczs.net/vehicle_back/20250630/fb9b0d98415ffec03b4f29a68d384f2c\\\"}}]\",\"result\":\"{\\\"code\\\":0,\\\"data\\\":{\\\"signedCount\\\":5,\\\"urls\\\":{\\\"520\\\":\\\"http://dev-cos.etczs.net/idcard_back/20250630/a988d1e2b368b5fce0c5eed8b6db0bdb?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLWp7jOpBPSMkzFMI6lHk7G8sRmtujYNA%26q-sign-time%3D1751434519%3B1751456119%26q-key-time%3D1751434519%3B1751456119%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%3D945114c1ae9cc9e4039d80d3cab35a0be194358d\\\",\\\"422\\\":\\\"http://dev-cos.etczs.net/idcard_front/20250630/05aa59c2c3c13bd91998081fec4159ef?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLWp7jOpBPSMkzFMI6lHk7G8sRmtujYNA%26q-sign-time%3D1751434519%3B1751456119%26q-key-time%3D1751434519%3B1751456119%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%3De09b775c85e75524023b65a8e25724a780fdc9c9\\\",\\\"1054\\\":\\\"https://wecos.etczs.net/assets/images/default_car_front_1017-0.jpg\\\",\\\"1142\\\":\\\"http://dev-cos.etczs.net/idcard_hold/20250702/d27c3e0ceaf422e84fed496e3f5731d1?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLWp7jOpBPSMkzFMI6lHk7G8sRmtujYNA%26q-sign-time%3D1751434519%3B1751456119%26q-key-time%3D1751434519%3B1751456119%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%3D2db1c359bd55e2c055c742bcd930214338822f71\\\",\\\"736\\\":\\\"http://dev-cos.etczs.net/vehicle_front/20250630/65940a8d4a2ca2b234723fbe71827109?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLWp7jOpBPSMkzFMI6lHk7G8sRmtujYNA%26q-sign-time%3D1751434519%3B1751456119%26q-key-time%3D1751434519%3B1751456119%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%3D23910af47cdfc942b74c0512eda8fd68fb197c11\\\",\\\"836\\\":\\\"http://dev-cos.etczs.net/vehicle_back/20250630/fb9b0d98415ffec03b4f29a68d384f2c?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLWp7jOpBPSMkzFMI6lHk7G8sRmtujYNA%26q-sign-time%3D1751434519%3B1751456119%26q-key-time%3D1751434519%3B1751456119%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%******************************************\\\"}},\\\"msg\\\":\\\"success\\\"}\",\"usedTime\":\"76ms\"}","thrown":""}
{"sysTime":"2025-07-02 13:35:19.255","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"699806f3-535a-47b4-a017-ae704181d9cd","spanId":"0","parentId":"","exportable":"","pid":"33621","thread":"http-nio-20130-exec-7","className":"c.e.s.filter.HttpServletResponseFilter:44","methodName":"c.e.s.filter.HttpServletResponseFilter:doFilter-44","message":"{\"code\":0,\"msg\":\"success\",\"data\":{\"id\":8,\"riskReviewSn\":\"2507021100000000161\",\"riskType\":2,\"riskTypeStr\":\"复审\",\"businessSn\":\"250630165900262201A1\",\"riskReviewStatus\":0,\"riskReviewStatusStr\":\"待审核\",\"rejectReasonId\":0,\"rejectReason\":\"\",\"previewRiskReviewSn\":\"2507012052000000151\",\"previewRejectReason\":\"请进行实名认证及车辆资料补传\",\"riskRuleRemark\":\"收货手机号存在超过两笔激活成功且未注销的订单;\",\"reviewInfo\":{\"reviewSn\":\"2507011826000002611\",\"idCardFrontUrl\":\"http://dev-cos.etczs.net/idcard_front/20250630/05aa59c2c3c13bd91998081fec4159ef?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLWp7jOpBPSMkzFMI6lHk7G8sRmtujYNA%26q-sign-time%3D1751434519%3B1751456119%26q-key-time%3D1751434519%3B1751456119%26q-header-list%3Dhost%26q-url-param-list%3D%26q-signature%3De09b775c85e75524023b65a8e25724a780fdc9c9\",\"idCardBackUrl\":\"http://dev-cos.etczs.net/idcard_back/20250630/a988d1e2b368b5fce0c5eed8b6db0bdb?sign=q-sign-algorithm%3Dsha1%26q-ak%3DAKIDLWp7jOpBPSMkzFMI6lHk7G8sRmtujYNA%26q-sign-time%3D1751434519%3B1751456119%26q-key-time%3D1751434519%3B...","thrown":""}
{"sysTime":"2025-07-02 13:35:47.812","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"22f71c48-d8b7-45df-b547-0fa8ebcb3e12","spanId":"0.1","parentId":"0","exportable":"","pid":"33621","thread":"ConsumeMessageThread_ets_java_delivery_group_1","className":"com.ets.starter.base.BaseConsumer:63","methodName":"com.ets.starter.base.BaseConsumer:invokeByJsonBody-63","message":"队列消费：{\"attempts\":1,\"beanName\":\"TaskJobBean\",\"className\":\"com.ets.delivery.application.app.disposer.TaskDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"createdAt\":1751434547757,\"nextExecTime\":1751434546726,\"execTimes\":0,\"referSn\":\"2507021100000000161\",\"referType\":\"risk_review_result_notify\",\"id\":2447,\"taskSn\":\"2507021335000008011\",\"status\":0,\"updatedAt\":1751434547757},\"retry\":0,\"spanId\":\"0.1\",\"traceId\":\"22f71c48-d8b7-45df-b547-0fa8ebcb3e12\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-07-02 13:35:48.806","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"22f71c48-d8b7-45df-b547-0fa8ebcb3e12","spanId":"0.1","parentId":"0","exportable":"","pid":"33621","thread":"ConsumeMessageThread_ets_java_delivery_group_1","className":"com.ets.common.aspect.MicroFeignAspect:85","methodName":"com.ets.common.aspect.MicroFeignAspect:saveLog-85","message":"{\"feign\":\"HardCodedTarget(type=PhpApplyFeign, name=PhpApplyFeign, url=http://etc-apply:80)\",\"annotations\":\"[\\\"@org.springframework.web.bind.annotation.PostMapping(consumes={}, headers={}, name=\\\\\\\"\\\\\\\", params={}, path={\\\\\\\"/notify/order/order-risk\\\\\\\"}, produces={}, value={\\\\\\\"/notify/order/order-risk\\\\\\\"})\\\",\\\"@org.springframework.web.bind.annotation.RequestMapping(consumes={}, headers={}, method={POST}, name=\\\\\\\"\\\\\\\", params={}, path={}, produces={}, value={})\\\"]\",\"parameterNames\":\"[\\\"notifyDTO\\\"]\",\"args\":\"[{\\\"msg\\\":\\\"证件照不清晰，请重新上传\\\",\\\"order_sn\\\":\\\"250630165900262201A1\\\",\\\"risk_review_status\\\":4,\\\"risk_type\\\":2}]\",\"result\":\"\\\"{\\\\\\\"code\\\\\\\":0,\\\\\\\"msg\\\\\\\":\\\\\\\"success\\\\\\\",\\\\\\\"data\\\\\\\":null}\\\"\",\"usedTime\":\"213ms\"}","thrown":""}
{"sysTime":"2025-07-02 13:38:28.278","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"Thread-17","className":"c.a.nacos.common.notify.NotifyCenter:145","methodName":"c.a.nacos.common.notify.NotifyCenter:shutdown-145","message":"{\"msg\":\"[NotifyCenter] Start destroying Publisher\"}","thrown":""}
{"sysTime":"2025-07-02 13:38:28.278","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:108","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-108","message":"{\"msg\":\"[HttpClientBeanHolder] Start destroying common HttpClient\"}","thrown":""}
{"sysTime":"2025-07-02 13:38:28.281","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"Thread-17","className":"c.a.nacos.common.notify.NotifyCenter:162","methodName":"c.a.nacos.common.notify.NotifyCenter:shutdown-162","message":"{\"msg\":\"[NotifyCenter] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-07-02 13:38:28.282","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:114","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-114","message":"{\"msg\":\"[HttpClientBeanHolder] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-07-02 13:38:28.302","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"c.a.c.n.registry.NacosServiceRegistry:95","methodName":"c.a.c.n.registry.NacosServiceRegistry:deregister-95","message":"De-registering from Nacos Server now...","thrown":""}
{"sysTime":"2025-07-02 13:38:28.302","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:100","methodName":"com.alibaba.nacos.client.naming:removeBeatInfo-100","message":"[BEAT] removing beat: apply@@delivery-application:************:20130 from beat map.","thrown":""}
{"sysTime":"2025-07-02 13:38:28.302","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:268","methodName":"com.alibaba.nacos.client.naming:deregisterService-268","message":"[DEREGISTER-SERVICE] ets-dev deregistering service apply@@delivery-application with instance: Instance{instanceId='null', ip='************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}","thrown":""}
{"sysTime":"2025-07-02 13:38:28.338","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"c.a.c.n.registry.NacosServiceRegistry:115","methodName":"c.a.c.n.registry.NacosServiceRegistry:deregister-115","message":"De-registration finished.","thrown":""}
{"sysTime":"2025-07-02 13:38:28.339","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:146","methodName":"com.alibaba.nacos.client.naming:shutdown-146","message":"com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin","thrown":""}
{"sysTime":"2025-07-02 13:38:31.353","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:148","methodName":"com.alibaba.nacos.client.naming:shutdown-148","message":"com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop","thrown":""}
{"sysTime":"2025-07-02 13:38:31.354","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:423","methodName":"com.alibaba.nacos.client.naming:shutdown-423","message":"com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin","thrown":""}
{"sysTime":"2025-07-02 13:38:32.994","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:248","methodName":"com.alibaba.nacos.client.naming:processServiceJson-248","message":"removed ips(1) service: apply@@delivery-application -> [{\"instanceId\":\"************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-07-02 13:38:32.994","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent","className":"c.e.deploy.NacosDynamicServerListWatcher:42","methodName":"c.e.deploy.NacosDynamicServerListWatcher:lambda$startWatch$0-42","message":"监听到nacos服务变更：serviceName: apply@@delivery-application, groupName：apply, 实例：[Instance{instanceId='*************#20130#DEFAULT#apply@@delivery-application', ip='*************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}]","thrown":""}
{"sysTime":"2025-07-02 13:38:32.996","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:281","methodName":"com.alibaba.nacos.client.naming:processServiceJson-281","message":"current ips:(1) service: apply@@delivery-application -> [{\"instanceId\":\"*************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"*************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"ipDeleteTimeout\":30000,\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000}]","thrown":""}
{"sysTime":"2025-07-02 13:38:32.997","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:132","methodName":"com.alibaba.nacos.client.naming:shutdown-132","message":"com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin","thrown":""}
{"sysTime":"2025-07-02 13:38:36.012","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:136","methodName":"com.alibaba.nacos.client.naming:shutdown-136","message":"com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop","thrown":""}
{"sysTime":"2025-07-02 13:38:36.015","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:132","methodName":"com.alibaba.nacos.client.naming:shutdown-132","message":"com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin","thrown":""}
{"sysTime":"2025-07-02 13:38:36.015","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:134","methodName":"com.alibaba.nacos.client.naming:shutdown-134","message":"com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop","thrown":""}
{"sysTime":"2025-07-02 13:38:36.015","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:428","methodName":"com.alibaba.nacos.client.naming:shutdown-428","message":"com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop","thrown":""}
{"sysTime":"2025-07-02 13:38:36.016","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:783","methodName":"com.alibaba.nacos.client.naming:shutdown-783","message":"com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin","thrown":""}
{"sysTime":"2025-07-02 13:38:36.016","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:72","methodName":"com.alibaba.nacos.client.naming:shutdown-72","message":"{\"msg\":\"[NamingHttpClientManager] Start destroying NacosRestTemplate\"}","thrown":""}
{"sysTime":"2025-07-02 13:38:36.016","level":"WARN","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:79","methodName":"com.alibaba.nacos.client.naming:shutdown-79","message":"{\"msg\":\"[NamingHttpClientManager] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-07-02 13:38:36.016","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"c.a.n.client.identify.CredentialWatcher:105","methodName":"c.a.n.client.identify.CredentialWatcher:stop-105","message":"[null] CredentialWatcher is stopped","thrown":""}
{"sysTime":"2025-07-02 13:38:36.017","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"c.a.n.client.identify.CredentialService:98","methodName":"c.a.n.client.identify.CredentialService:free-98","message":"[null] CredentialService is freed","thrown":""}
{"sysTime":"2025-07-02 13:38:36.017","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:787","methodName":"com.alibaba.nacos.client.naming:shutdown-787","message":"com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop","thrown":""}
{"sysTime":"2025-07-02 13:38:36.703","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"Thread-35","className":"com.xxl.rpc.remoting.net.Server:74","methodName":"com.xxl.rpc.remoting.net.Server:run-74","message":">>>>>>>>>>> xxl-rpc remoting server stop.","thrown":""}
{"sysTime":"2025-07-02 13:38:36.749","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"xxl-job, executor ExecutorRegistryThread","className":"c.x.j.core.thread.ExecutorRegistryThread:87","methodName":"c.x.j.core.thread.ExecutorRegistryThread:run-87","message":">>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='etc-java-delivery-executor-dev', registryValue='**********:20132'}, registryResult:ReturnT [code=200, msg=null, content=null]","thrown":""}
{"sysTime":"2025-07-02 13:38:36.750","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"xxl-job, executor ExecutorRegistryThread","className":"c.x.j.core.thread.ExecutorRegistryThread:105","methodName":"c.x.j.core.thread.ExecutorRegistryThread:run-105","message":">>>>>>>>>>> xxl-job, executor registry thread destory.","thrown":""}
{"sysTime":"2025-07-02 13:38:36.750","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.xxl.rpc.remoting.net.Server:110","methodName":"com.xxl.rpc.remoting.net.Server:stop-110","message":">>>>>>>>>>> xxl-rpc remoting server destroy success.","thrown":""}
{"sysTime":"2025-07-02 13:38:36.751","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"xxl-job, executor TriggerCallbackThread","className":"c.x.j.core.thread.TriggerCallbackThread:96","methodName":"c.x.j.core.thread.TriggerCallbackThread:run-96","message":">>>>>>>>>>> xxl-job, executor callback thread destory.","thrown":""}
{"sysTime":"2025-07-02 13:38:36.751","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"Thread-34","className":"c.x.j.core.thread.TriggerCallbackThread:126","methodName":"c.x.j.core.thread.TriggerCallbackThread:run-126","message":">>>>>>>>>>> xxl-job, executor retry callback thread destory.","thrown":""}
{"sysTime":"2025-07-02 13:38:36.914","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"c.b.d.d.DynamicRoutingDataSource:211","methodName":"c.b.d.d.DynamicRoutingDataSource:destroy-211","message":"dynamic-datasource start closing ....","thrown":""}
{"sysTime":"2025-07-02 13:38:36.918","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-2} closing ...","thrown":""}
{"sysTime":"2025-07-02 13:38:36.919","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-2} closed","thrown":""}
{"sysTime":"2025-07-02 13:38:36.919","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-1} closing ...","thrown":""}
{"sysTime":"2025-07-02 13:38:36.919","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-1} closed","thrown":""}
{"sysTime":"2025-07-02 13:38:36.920","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-4} closing ...","thrown":""}
{"sysTime":"2025-07-02 13:38:36.920","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-4} closed","thrown":""}
{"sysTime":"2025-07-02 13:38:36.920","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-3} closing ...","thrown":""}
{"sysTime":"2025-07-02 13:38:36.920","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-3} closed","thrown":""}
{"sysTime":"2025-07-02 13:38:36.920","level":"INFO","ip":"127.0.0.1","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"33621","thread":"SpringApplicationShutdownHook","className":"c.b.d.d.DynamicRoutingDataSource:215","methodName":"c.b.d.d.DynamicRoutingDataSource:destroy-215","message":"dynamic-datasource all closed success,bye","thrown":""}
