package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.common.util.ThreadLocalUtil;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.app.thirdservice.feign.RiskFeign;
import com.ets.delivery.application.app.thirdservice.request.risk.RiskRuleQueryDTO;
import com.ets.delivery.application.app.thirdservice.response.RiskRuleQueryVO;
import com.ets.delivery.application.common.config.DeliveryConfig;
import com.ets.delivery.application.common.consts.ThreadLocalCacheKey;
import com.ets.delivery.application.common.consts.reviews.ReviewsRiskStatusEnum;
import com.ets.delivery.application.common.consts.riskreview.RiskReviewAutoAuditEnum;
import com.ets.delivery.application.common.consts.riskreview.RiskReviewRiskTypeEnum;
import com.ets.delivery.application.common.consts.riskreview.RiskReviewStatusEnum;
import com.ets.delivery.application.common.consts.setting.SettingCategoryEnum;
import com.ets.delivery.application.common.consts.setting.SettingKeyEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.delivery.application.common.dto.riskreview.*;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.common.vo.MultiSelectOptionVO;
import com.ets.delivery.application.common.vo.riskreview.AdminRiskReviewsSelectOptionVO;
import com.ets.delivery.application.common.vo.riskreview.RiskReviewDetailVO;
import com.ets.delivery.application.common.vo.riskreview.RiskReviewListVO;
import com.ets.delivery.application.common.vo.riskreview.RiskReviewLogListVO;
import com.ets.delivery.application.infra.entity.*;
import com.ets.delivery.application.infra.entity.riskreview.RiskReviews;
import com.ets.delivery.application.infra.entity.riskreview.RiskReviewsInfo;
import com.ets.delivery.application.infra.entity.riskreview.RiskReviewsLog;
import com.ets.delivery.application.infra.service.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class RiskReviewBusiness {

    @Autowired
    private RiskReviewsService riskReviewsService;

    @Autowired
    private RiskReviewsInfoService riskReviewsInfoService;

    @Autowired
    private RiskReviewsLogService riskReviewsLogService;

    @Autowired
    private ReviewsService reviewsService;

    @Autowired
    private ReviewsIdcardsService reviewsIdcardsService;

    @Autowired
    private ReviewsVehiclesService reviewsVehiclesService;

    @Autowired
    private SettingService settingService;

    @Autowired
    private RiskReviewQueueBusiness riskReviewQueueBusiness;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Value("${spring.profiles.active}")
    private String active;

    @Autowired
    private DeliveryConfig deliveryConfig;

    @Autowired
    private RiskFeign riskFeign;

    /**
     * 创建风控审核单
     *
     * @param dto 创建风控审核单请求参数
     * @return 风控单号
     */
    public String createRiskReview(RiskReviewCreateDTO dto) {
        // 加锁，防止并发操作
        String lockKey = "riskReview:" + dto.getBusinessSn();
        if (!ToolsHelper.addLock(redisPermanentTemplate, lockKey, 30)) {
            ToolsHelper.throwException("风控审核单创建中，请稍后再试");
        }

        try {
            // 检查业务单是否存在待审核记录
            RiskReviews existingReview = riskReviewsService.getByBusinessSn(dto.getBusinessSn());
            if (existingReview != null && existingReview.getRiskReviewStatus().equals(RiskReviewStatusEnum.PENDING.getValue())) {
                ToolsHelper.throwException("该业务单已存在待审核记录，请勿重复提交");
            }

            // 生成风控单号
            String riskReviewSn = ToolsHelper.genNum(redisPermanentTemplate, "RiskReview", active, 8);

            // 申办业务才需要查询审核单并记录到审核资料表
            if (dto.getBusinessType().equals(1)) { // 1-申办
                //todo 检查处理
                Reviews reviews = reviewsService.getOneByOrderSnAndRiskStatus(dto.getBusinessSn(), ReviewsRiskStatusEnum.IN_PROGRESS.getValue());
                if (reviews != null) {
                    RiskReviewsInfo reviewsInfo = new RiskReviewsInfo();
                    reviewsInfo.setRiskReviewSn(riskReviewSn);
                    reviewsInfo.setReviewSn(reviews.getReviewSn());
                    riskReviewsInfoService.create(reviewsInfo);

                    log.info("风控审核单关联申办审核单成功，风控单号：{}，申办审核单号：{}", riskReviewSn, reviews.getReviewSn());
                } else {
                    log.warn("未找到对应的申办审核单，业务单号：{}", dto.getBusinessSn());
                    ToolsHelper.throwException("未找到对应的申办审核单");
                }
            } else {
                log.info("非申办业务，无需关联申办审核单，风控单号：{}，业务类型：{}", riskReviewSn, dto.getBusinessType());
            }

            // 创建风控审核单
            RiskReviews riskReviews = BeanUtil.copyProperties(dto, RiskReviews.class);

            // 保存风控审核单
            riskReviews.setRiskReviewSn(riskReviewSn);
            riskReviews.setRiskReviewStatus(RiskReviewStatusEnum.PENDING.getValue()); // 待审核
            riskReviewsService.create(riskReviews);

            // 推送到初审队列
            riskReviewQueueBusiness.pushToRiskReviewQueueList(riskReviewSn, RiskReviewRiskTypeEnum.FIRST_REVIEW.getValue());

            // 记录操作日志
            RiskReviewsLog reviewsLog = new RiskReviewsLog();
            reviewsLog.setRiskReviewSn(riskReviewSn);
            reviewsLog.setOperateContent("创建风控审核单");
            reviewsLog.setOperator("system");
            reviewsLog.setType("create");
            riskReviewsLogService.create(reviewsLog);

            log.info("创建风控审核单成功，风控单号：{}，业务单号：{}，风控流水号：{}",
                    riskReviewSn, dto.getBusinessSn(), dto.getRiskSn());

            return riskReviewSn;
        } finally {
            // 释放锁
            ToolsHelper.unLock(redisPermanentTemplate, lockKey);
        }
    }

    public void cancelRiskReview(RiskReviewCancelDTO cancelDTO) {
        // 通过业务单号查询风控审核单记录
        RiskReviews riskReviews = riskReviewsService.getByBusinessSn(cancelDTO.getBusinessSn());
        if (riskReviews == null) {
            ToolsHelper.throwException("风控审核单不存在");
        }

        // 检查风控单状态
        if (riskReviews.getRiskReviewStatus().equals(RiskReviewStatusEnum.CANCELED.getValue())) {
            return;
        }

        // 已有审核结果 无需取消
        if (!riskReviews.getRiskReviewStatus().equals(RiskReviewStatusEnum.PENDING.getValue())) {
            log.warn("业务单号对应风控审核单已有审核结果，无需取消，风控单号：{}，业务单号：{}",
                    riskReviews.getRiskReviewSn(), riskReviews.getBusinessSn());
            return;
        }

        // 更新风控审核单状态
        riskReviews.setRiskReviewStatus(RiskReviewStatusEnum.CANCELED.getValue());
        riskReviews.setRiskReviewRemark(cancelDTO.getCancelReason());
        riskReviewsService.updateById(riskReviews);

        // 记录操作日志
        RiskReviewsLog reviewsLog = new RiskReviewsLog();
        reviewsLog.setRiskReviewSn(riskReviews.getRiskReviewSn());
        reviewsLog.setOperateContent("取消风控审核单，取消原因：" + cancelDTO.getCancelReason());
        reviewsLog.setOperator("system");
        reviewsLog.setType("cancel");
        riskReviewsLogService.create(reviewsLog);

        log.info("取消风控审核单成功，风控单号：{}，业务单号：{}",
                riskReviews.getRiskReviewSn(), riskReviews.getBusinessSn());

    }

    public void riskInfoUpload(RiskInfoUploadDTO uploadDTO) {
        // 通过业务单号查询风控审核单记录
        RiskReviews riskReviews = riskReviewsService.getByBusinessSn(uploadDTO.getBusinessSn());
        if (riskReviews == null) {
            ToolsHelper.throwException("风控审核单不存在");
        }

        // 幂等处理 最新一个风控审核单图片一样 直接返回
        RiskReviewsInfo existingReviewInfo = riskReviewsInfoService.getByRiskReviewSn(riskReviews.getRiskReviewSn());
        if (existingReviewInfo != null &&
                existingReviewInfo.getAdditionalImgUrl() != null &&
                existingReviewInfo.getAdditionalImgUrl().equals(uploadDTO.getAdditionalImgUrl())
        ) {
            return;
        }

        // 检查风控单状态
        if (!riskReviews.getRiskReviewStatus().equals(RiskReviewStatusEnum.SUPPLEMENT_MATERIAL.getValue())) {
            ToolsHelper.throwException("风控审核单非补充资料状态，无法上传补充资料");
        }

        // 生成风控单号
        String riskReviewSn = ToolsHelper.genNum(redisPermanentTemplate, "RiskReview", active, 8);

        // 创建复审资料
        RiskReviewsInfo reviewsInfo = new RiskReviewsInfo();
        reviewsInfo.setRiskReviewSn(riskReviewSn);
        if (existingReviewInfo != null) {
            reviewsInfo.setReviewSn(existingReviewInfo.getReviewSn());
        }
        reviewsInfo.setAdditionalImgUrl(uploadDTO.getAdditionalImgUrl());
        riskReviewsInfoService.create(reviewsInfo);

        // 创建复审记录
        RiskReviews recheckReview = new RiskReviews();
        recheckReview.setRiskReviewSn(riskReviewSn);
        recheckReview.setRiskSn(riskReviews.getRiskSn());
        recheckReview.setRiskRuleIds(riskReviews.getRiskRuleIds());
        recheckReview.setRiskRuleRemark(riskReviews.getRiskRuleRemark());
        recheckReview.setBusinessType(riskReviews.getBusinessType());
        recheckReview.setBusinessSn(riskReviews.getBusinessSn());
        recheckReview.setIssuerId(riskReviews.getIssuerId());
        recheckReview.setPlateNo(riskReviews.getPlateNo());
        recheckReview.setPlateColor(riskReviews.getPlateColor());
        recheckReview.setUid(riskReviews.getUid());
        recheckReview.setRiskType(RiskReviewRiskTypeEnum.RECHECK_REVIEW.getValue());
        recheckReview.setRiskReviewStatus(RiskReviewStatusEnum.PENDING.getValue()); // 待审核
        riskReviewsService.create(recheckReview);

        // 推送到复审队列
        riskReviewQueueBusiness.pushToRiskReviewQueueList(riskReviewSn, RiskReviewRiskTypeEnum.RECHECK_REVIEW.getValue());

        // 记录操作日志
        RiskReviewsLog reviewsLog = new RiskReviewsLog();
        reviewsLog.setRiskReviewSn(riskReviewSn);
        reviewsLog.setOperateContent("上传补充资料");
        reviewsLog.setOperator("system");
        reviewsLog.setType("upload");
        riskReviewsLogService.create(reviewsLog);

        log.info("上传补充资料成功，风控单号：{}，业务单号：{}，风控流水号：{}",
                riskReviewSn, riskReviews.getBusinessSn(), riskReviews.getRiskSn());
    }

    public IPage<RiskReviewListVO> getList(RiskReviewListDTO listDTO) {
        // 时间处理
        if (StringUtils.isNotEmpty(listDTO.getCreateStartTime())) {
            listDTO.setCreateStartTime(listDTO.getCreateStartTime() + " 00:00:00");
        }
        if (StringUtils.isNotEmpty(listDTO.getCreateEndTime())) {
            listDTO.setCreateEndTime(listDTO.getCreateEndTime() + " 23:59:59");
        }

        // 时间范围最大31天
        if (StringUtils.isNotEmpty(listDTO.getCreateStartTime()) && StringUtils.isNotEmpty(listDTO.getCreateEndTime())) {
            if (LocalDateTime.parse(listDTO.getCreateStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).plusDays(31)
                    .isBefore(LocalDateTime.parse(listDTO.getCreateEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))) {
                ToolsHelper.throwException("时间范围不能超过31天");
            }
        }

        // 风控原因id筛选 必须带上时间条件
        if (StringUtils.isNotEmpty(listDTO.getRiskRuleId()) && (StringUtils.isEmpty(listDTO.getCreateStartTime()) || StringUtils.isEmpty(listDTO.getCreateEndTime()))) {
            ToolsHelper.throwException("风控原因筛选必须带上时间条件");
        }

        return riskReviewsService.getList(listDTO).convert(risk -> {
            RiskReviewListVO listVO = BeanUtil.copyProperties(risk, RiskReviewListVO.class);
            listVO.setIssuerName(deliveryConfig.getCnNameByIssuerId(risk.getIssuerId()));
            return listVO;
        });
    }

    /**
     * 获取风控审核详情
     *
     * @param detailDTO 详情查询参数
     * @return 风控审核详情
     */
    public RiskReviewDetailVO getDetail(RiskReviewDetailDTO detailDTO) {
        // 查询风控审核单
        RiskReviews riskReviews = riskReviewsService.getByRiskReviewSn(detailDTO.getRiskReviewSn());
        if (riskReviews == null) {
            ToolsHelper.throwException("风控审核单不存在");
        }

        // 转换为VO对象
        RiskReviewDetailVO detailVO = BeanUtil.copyProperties(riskReviews, RiskReviewDetailVO.class);

        // 查询风控资料信息
        RiskReviewsInfo riskReviewsInfo = riskReviewsInfoService.getByRiskReviewSn(riskReviews.getRiskReviewSn());
        if (riskReviewsInfo != null) {
            // 创建审核资料信息对象
            RiskReviewDetailVO.ReviewInfo reviewInfo = new RiskReviewDetailVO.ReviewInfo();
            reviewInfo.setReviewSn(riskReviewsInfo.getReviewSn());
            reviewInfo.setAdditionalImgUrl(riskReviewsInfo.getAdditionalImgUrl());

            // 如果有关联的审核单，查询详细资料
            if (StringUtils.isNotEmpty(riskReviewsInfo.getReviewSn())) {
                Reviews reviews = reviewsService.getOneByReviewSn(riskReviewsInfo.getReviewSn());
                if (reviews != null) {
                    // 获取证件信息
                    ReviewsIdcards reviewsIdcards = reviewsIdcardsService.getOneByReviewSn(reviews.getReviewSn());
                    if (reviewsIdcards != null) {
                        reviewInfo.setIdCardFrontUrl(reviewsIdcards.getFrontImgUrl());
                        reviewInfo.setIdCardBackUrl(reviewsIdcards.getBackImgUrl());
                        reviewInfo.setIdCardType(reviewsIdcards.getType());
                        reviewInfo.setIdCardName(reviewsIdcards.getName());
                        reviewInfo.setIdCardNumber(reviewsIdcards.getNumber());
                    }

                    // 获取车辆信息
                    ReviewsVehicles reviewsVehicles = reviewsVehiclesService.getOneByReviewSn(reviews.getReviewSn());
                    if (reviewsVehicles != null) {
                        reviewInfo.setVehicleFrontUrl(reviewsVehicles.getFrontImgUrl());
                        reviewInfo.setVehicleBackUrl(reviewsVehicles.getBackImgUrl());
                        reviewInfo.setPlateNo(reviewsVehicles.getPlateNo());
                        reviewInfo.setPlateColor(reviewsVehicles.getPlateColor());
                        reviewInfo.setPassengers(reviewsVehicles.getPassengers());
                        reviewInfo.setVehicleType(reviewsVehicles.getType());
                        reviewInfo.setVin(reviewsVehicles.getVin());
                        reviewInfo.setCarImgUrl(reviewsVehicles.getCarImgUrl());
                    }
                }
            }

            detailVO.setReviewInfo(reviewInfo);
        }

        // 如果是复审单，查询初审单信息
        if (riskReviews.getRiskType().equals(RiskReviewRiskTypeEnum.RECHECK_REVIEW.getValue())) {
            // 根据业务单号查询初审单
            RiskReviews previewRiskReview = riskReviewsService.getByBusinessSnAndRiskType(riskReviews.getBusinessSn(), RiskReviewRiskTypeEnum.FIRST_REVIEW.getValue());
            if (previewRiskReview != null) {
                detailVO.setPreviewRiskReviewSn(previewRiskReview.getRiskReviewSn());
                detailVO.setPreviewRejectReason(previewRiskReview.getRejectReason());
            }
        }

        return detailVO;
    }

    /**
     * 获取风控审核日志列表
     *
     * @param logDTO 日志查询参数
     * @return 风控审核日志列表
     */
    public IPage<RiskReviewLogListVO> getLog(RiskReviewLogListDTO logDTO) {
        // 根据风控单号查询日志列表
        IPage<RiskReviewsLog> logPage = riskReviewsLogService.getLogList(logDTO);

        // 转换为VO对象
        return logPage.convert(log -> BeanUtil.copyProperties(log, RiskReviewLogListVO.class));
    }

    public AdminRiskReviewsSelectOptionVO getSelectOption() {
        AdminRiskReviewsSelectOptionVO optionVO = new AdminRiskReviewsSelectOptionVO();
        optionVO.setRiskTypeList(RiskReviewRiskTypeEnum.getSelectOptions());
        optionVO.setIssuerIdList(deliveryConfig.getIssuerMap().entrySet().stream()
                .map(entry -> Map.of("label", entry.getValue().getCnName(), "value", entry.getKey().toString()))
                .toList());
        optionVO.setRiskReviewStatusList(RiskReviewStatusEnum.getSelectOptions());
        optionVO.setAutoAuditList(RiskReviewAutoAuditEnum.getSelectOptions());

        // 获取驳回原因（二级联动）
        List<Setting> riskReviewReasonList = settingService.getRiskReviewReasonList();
        Map<String, Map<String, String>> reasonMap = riskReviewReasonList.stream()
                .collect(Collectors.groupingBy(Setting::getCategory,
                        Collectors.toMap(Setting::getParams, Setting::getValue, (v1, v2) -> v1)));

        // 驳回原因分类
        List<MultiSelectOptionVO> reasonList = SettingCategoryEnum.riskReviewList.stream().map(category -> {
            MultiSelectOptionVO multiSelectOptionVO = new MultiSelectOptionVO();
            multiSelectOptionVO.setLabel(SettingCategoryEnum.map.get(category));
            multiSelectOptionVO.setValue(category);

            // 将每个原因转换为 MultiSelectOptionVO 对象
            Map<String, String> categoryReasons = reasonMap.getOrDefault(category, Collections.emptyMap());
            List<MultiSelectOptionVO> childrenList = categoryReasons.entrySet().stream()
                    .map(entry -> {
                        MultiSelectOptionVO childOption = new MultiSelectOptionVO();
                        childOption.setLabel(entry.getValue());  // 设置显示文本为value字段
                        childOption.setValue(entry.getKey());  // 设置值为id字段
                        return childOption;
                    })
                    .toList();

            multiSelectOptionVO.setChildren(childrenList);
            return multiSelectOptionVO;
        }).toList();
        optionVO.setRejectReasonList(reasonList);

        try {
            // 获取匹配规则
            RiskRuleQueryDTO riskRuleQueryDTO = new RiskRuleQueryDTO();
            riskRuleQueryDTO.setBusinessType(1);
            riskRuleQueryDTO.setRuleType(3);
            JsonResult<List<RiskRuleQueryVO>> jsonResult = riskFeign.getRuleByParams(riskRuleQueryDTO);
            jsonResult.checkError();
            List<Map<String, String>> riskRuleList = new ArrayList<>();
            jsonResult.getData().forEach(riskRuleQueryVO ->
                    riskRuleList.add(Map.of("label", riskRuleQueryVO.getRuleName(), "value", riskRuleQueryVO.getRuleId().toString())));
            optionVO.setRiskRuleList(riskRuleList);
        } catch (Exception e) {
            log.error("获取风控匹配规则失败：{}", e.getLocalizedMessage());
        }

        return optionVO;
    }

    /**
     * 获取风控初审数据（每次只获取一条）
     * @return 风控审核详情
     */
    public RiskReviewDetailVO receive(RiskReviewReceiveDTO receiveDTO) {
        return getOneRiskReviewByType(RiskReviewRiskTypeEnum.FIRST_REVIEW.getValue());
    }

    /**
     * 获取风控复审数据（每次只获取一条）
     * @return 风控审核详情
     */
    public RiskReviewDetailVO receiveRecheck(RiskReviewReceiveDTO receiveDTO) {
        return getOneRiskReviewByType(RiskReviewRiskTypeEnum.RECHECK_REVIEW.getValue());
    }

    /**
     * 从队列获取指定类型的风控审核单
     * @param riskType 风控类型 1-初审 2-复审
     * @return 风控审核详情
     */
    private RiskReviewDetailVO getOneRiskReviewByType(Integer riskType) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        if (user == null) {
            log.warn("获取当前用户信息失败");
            return null;
        }

        // 1. 优先获取用户已领取的指定类型记录
        List<String> userRiskReviewList = riskReviewQueueBusiness.getUserRiskReviewList(user.getUsername(), riskType);
        if (userRiskReviewList != null && !userRiskReviewList.isEmpty()) {
            for (String riskReviewSn : userRiskReviewList) {
                try {
                    RiskReviewDetailDTO detailDTO = new RiskReviewDetailDTO();
                    detailDTO.setRiskReviewSn(riskReviewSn);
                    RiskReviewDetailVO riskReviewDetailVO = this.getDetail(detailDTO);

                    // 检查状态为待审核（类型已经通过key区分了）
                    if (riskReviewDetailVO != null &&
                        riskReviewDetailVO.getRiskReviewStatus().equals(RiskReviewStatusEnum.PENDING.getValue())) {
                        log.info("用户 {} 获取已领取的风控审核单：{}，类型：{}", user.getUsername(), riskReviewSn, riskType);
                        return riskReviewDetailVO;
                    }
                } catch (Exception e) {
                    log.error("获取风控审核单详情失败，风控单号：{}，类型：{}，错误：{}", riskReviewSn, riskType, e.getLocalizedMessage());
                    riskReviewQueueBusiness.removeUserRiskReview(user.getUsername(), riskReviewSn, riskType);
                }
            }
        }

        // 2. 没有已领取的记录，从队列获取新的
        RiskReviewDetailVO riskReviewDetailVO = null;
        String riskReviewSn = riskReviewQueueBusiness.popFromRiskReviewQueueList(riskType);

        if (StringUtils.isEmpty(riskReviewSn)) {
            // 休眠100毫秒以防更新事务未完成
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                log.error("sleep error {}", e.getLocalizedMessage());
            }

            // 搜索数据库中待审核的风控审核单（按类型获取）
            List<RiskReviews> riskReviewsList = riskReviewsService.getDefaultListLimitDate(riskType, 7, 50);

            // 重新入队列
            if (riskReviewsList != null && !riskReviewsList.isEmpty()) {
                riskReviewsList.stream()
                        .filter(riskReviews -> riskReviews.getOperator().isEmpty())
                        .forEach(riskReview ->
                        riskReviewQueueBusiness.pushToRiskReviewQueueList(riskReview.getRiskReviewSn(), riskType));
                riskReviewSn = riskReviewQueueBusiness.popFromRiskReviewQueueList(riskType);
            }
        }

        if (StringUtils.isNotEmpty(riskReviewSn)) {
            RiskReviewDetailDTO detailDTO = new RiskReviewDetailDTO();
            detailDTO.setRiskReviewSn(riskReviewSn);
            riskReviewDetailVO = this.getDetail(detailDTO);

            // 确保返回的是正确类型的审核单
            if (riskReviewDetailVO != null && !riskReviewDetailVO.getRiskType().equals(riskType)) {
                log.warn("获取到错误类型的审核单，重新获取，风控单号：{}，期望类型：{}，实际类型：{}",
                        riskReviewSn, riskType, riskReviewDetailVO.getRiskType());
                return getOneRiskReviewByType(riskType);
            }

            // 添加到用户领取记录（按类型）
            if (riskReviewDetailVO != null) {
                // 更新审核单操作人
                LambdaUpdateWrapper<RiskReviews> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(RiskReviews::getRiskReviewSn, riskReviewSn)
                        .set(RiskReviews::getOperator, user.getUsername())
                        .set(RiskReviews::getUpdatedAt, LocalDateTime.now());
                riskReviewsService.updateByWrapper(updateWrapper);

                riskReviewQueueBusiness.addUserRiskReview(user.getUsername(), riskReviewSn, riskType);
                log.info("用户 {} 新领取风控审核单：{}，类型：{}", user.getUsername(), riskReviewSn, riskType);
            }
        }

        return riskReviewDetailVO;
    }

    /**
     * 提交风控初审结果
     * @param dto 提交参数
     * @return 下一条待审核数据（如果需要）
     */
    public RiskReviewDetailVO submitPreview(RiskReviewSubmitDTO dto) {
        return submitRiskReview(dto, RiskReviewRiskTypeEnum.FIRST_REVIEW.getValue());
    }

    /**
     * 提交风控复审结果
     * @param dto 提交参数
     * @return 下一条待审核数据（如果需要）
     */
    public RiskReviewDetailVO submitRecheck(RiskReviewSubmitDTO dto) {
        return submitRiskReview(dto, RiskReviewRiskTypeEnum.RECHECK_REVIEW.getValue());
    }

    /**
     * 提交风控审核结果的通用方法
     * @param dto 提交参数
     * @param expectedRiskType 期望的风控类型
     * @return 下一条待审核数据（如果需要）
     */
    private RiskReviewDetailVO submitRiskReview(RiskReviewSubmitDTO dto, Integer expectedRiskType) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        if (user == null) {
            ToolsHelper.throwException("获取当前用户信息失败");
        }

        // 加锁，防止并发操作
        String lockKey = "riskReviewSubmit:" + dto.getRiskReviewSn();
        if (!ToolsHelper.addLock(redisPermanentTemplate, lockKey, 30)) {
            ToolsHelper.throwException("风控审核单提交中，请稍后再试");
        }

        try {
            // 查询风控审核单
            RiskReviews riskReviews = riskReviewsService.getByRiskReviewSn(dto.getRiskReviewSn());
            if (riskReviews == null) {
                ToolsHelper.throwException("风控审核单不存在");
            }

            // 验证风控类型
            if (!riskReviews.getRiskType().equals(expectedRiskType)) {
                ToolsHelper.throwException("风控审核单类型不匹配");
            }

            // 验证状态
            if (!riskReviews.getRiskReviewStatus().equals(RiskReviewStatusEnum.PENDING.getValue())) {
                ToolsHelper.throwException("风控审核单状态不是待审核，无法提交");
            }

            // 处理驳回原因
            String rejectReason = null;
            if (dto.getRejectReasonId() != null && dto.getRejectReasonId() > 0) {
                Setting reason = settingService.getOneByParams(dto.getRejectReasonId().toString(), SettingKeyEnum.RISK_REVIEW.getValue());
                if (ObjectUtils.isEmpty(reason)) {
                    ToolsHelper.throwException("驳回原因不存在，请刷新页面重新选择");
                }
                rejectReason = reason.getValue();
            }

            // 更新风控审核单
            riskReviews.setRiskReviewStatus(dto.getRiskReviewStatus());
            riskReviews.setRejectReasonId(dto.getRejectReasonId());
            riskReviews.setRejectReason(rejectReason);
            riskReviews.setRiskReviewTime(LocalDateTime.now());
            riskReviews.setOperator(user.getUsername());
            riskReviews.setUpdatedAt(LocalDateTime.now());
            riskReviewsService.updateById(riskReviews);

            // 记录操作日志
            RiskReviewsLog reviewsLog = new RiskReviewsLog();
            reviewsLog.setRiskReviewSn(dto.getRiskReviewSn());
            reviewsLog.setOperateContent("提交审核结果：" + RiskReviewStatusEnum.map.get(dto.getRiskReviewStatus()));
            if (StringUtils.isNotEmpty(rejectReason)) {
                reviewsLog.setOperateContent(reviewsLog.getOperateContent() + "，驳回原因：" + rejectReason);
            }
            reviewsLog.setOperator(user.getUsername());
            reviewsLog.setType("submit");
            riskReviewsLogService.create(reviewsLog);

            // 根据结果通知
            TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
            taskRecordDTO.setReferSn(riskReviews.getRiskReviewSn());
            taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_RISK_REVIEW_RESULT_NOTIFY.getType());
            TaskFactory.create(TaskRecordReferTypeEnum.TASK_RISK_REVIEW_RESULT_NOTIFY).addAndPush(taskRecordDTO);

            // 从用户领取记录中移除
            riskReviewQueueBusiness.removeUserRiskReview(user.getUsername(), dto.getRiskReviewSn(), expectedRiskType);

            log.info("用户 {} 提交风控审核结果成功，风控单号：{}，状态：{}",
                    user.getUsername(), dto.getRiskReviewSn(), dto.getRiskReviewStatus());

            // 如果需要获取下一条数据
            if (dto.getNeedNext() != null && dto.getNeedNext()) {
                return getOneRiskReviewByType(expectedRiskType);
            }

            return null;
        } finally {
            // 释放锁
            ToolsHelper.unLock(redisPermanentTemplate, lockKey);
        }
    }

}