package com.ets.delivery.application.common.vo.riskreview;

import com.ets.delivery.application.common.consts.PlateColorEnum;
import com.ets.delivery.application.common.consts.riskreview.RiskReviewRiskTypeEnum;
import com.ets.delivery.application.common.consts.riskreview.RiskReviewStatusEnum;
import lombok.Data;

/**
 * 风控审核详情VO
 */
@Data
public class RiskReviewDetailVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 风控单号
     */
    private String riskReviewSn;

    /**
     * 风控类型[1-初审 2-复审]
     */
    private Integer riskType;

    private String riskTypeStr;

    /**
     * 业务单号
     */
    private String businessSn;

    /**
     * 风控审核状态[0-待审核 1-审核通过 2-审核驳回取消 3-审核驳回重新上传 4-补传资料 5-审核取消]
     */
    private Integer riskReviewStatus;

    private String riskReviewStatusStr;

    /**
     * 驳回原因id
     */
    private Integer rejectReasonId;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 初审风控单号
     */
    private String previewRiskReviewSn;

    /**
     * 初审驳回原因
     */
    private String previewRejectReason;

    /**
     * 触发风控原因
     */
    private String riskRuleRemark;

    /**
     * 审核资料
     */
    private ReviewInfo reviewInfo;

    public String getRiskTypeStr() {
        return RiskReviewRiskTypeEnum.map.getOrDefault(this.riskType, "未知");
    }

    public String getRiskReviewStatusStr() {
        return RiskReviewStatusEnum.map.getOrDefault(this.riskReviewStatus, "未知");
    }

    @Data
    public static class ReviewInfo {

        /**
         * 业务审核单号
         */
        private String reviewSn;

        /**
         * 证件照正反面
         */
        private String idCardFrontUrl;
        private String idCardBackUrl;
        private Integer idCardType;
        private String idCardName;
        private String idCardNumber;

        /**
         * 行驶证照正反面
         */
        private String vehicleFrontUrl;
        private String vehicleBackUrl;
        private String plateNo;
        private Integer plateColor;
        private String plateColorStr;
        private String passengers;
        private String vehicleType;
        private String vin;

        /**
         * 车头照
         */
        private String carImgUrl;

        /**
         * 补充图片url
         */
        private String additionalImgUrl;

        public String getPlateColorStr() {
            return PlateColorEnum.map.getOrDefault(this.plateColor, "-");
        }
    }
}
