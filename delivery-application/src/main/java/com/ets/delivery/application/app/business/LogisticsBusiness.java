package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.common.util.ThreadLocalUtil;
import com.ets.delivery.application.app.disposer.LogisticsOrderProcessDisposer;
import com.ets.delivery.application.app.factory.storage.StorageFactory;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.app.thirdservice.feign.GoodsFeign;
import com.ets.delivery.application.app.thirdservice.request.goods.GoodsSkuInfoDTO;
import com.ets.delivery.application.common.bo.logistics.LogisticsErpOrderCreateBO;
import com.ets.delivery.application.common.bo.logistics.LogisticsOneBO;
import com.ets.delivery.application.common.bo.logistics.LogisticsPageBO;
import com.ets.delivery.application.common.bo.logistics.LogisticsSkuCreateBO;
import com.ets.delivery.application.common.config.DeliveryConfig;
import com.ets.delivery.application.common.config.queue.express.QueueExpress;
import com.ets.delivery.application.common.consts.ErrCodeConstant;
import com.ets.delivery.application.common.consts.ThreadLocalCacheKey;
import com.ets.delivery.application.common.consts.logistics.*;
import com.ets.delivery.application.common.consts.storage.StorageCodeConstant;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeConstant;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.delivery.application.common.dto.logistics.*;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.common.vo.LogisticsAddOrderVO;
import com.ets.delivery.application.common.vo.addressMap.AddressMapInfoVO;
import com.ets.delivery.application.common.vo.logistics.*;
import com.ets.delivery.application.common.vo.pickUp.PickUpLogisticsInfoVO;
import com.ets.delivery.application.infra.entity.*;
import com.ets.delivery.application.infra.service.*;
import com.ets.delivery.feign.request.logistics.LogisticsCancelByOrderSnDTO;
import com.ets.delivery.feign.request.logistics.LogisticsFindByOrderSnDTO;
import com.ets.delivery.feign.request.pickup.PickupGoodsBO;
import com.ets.delivery.feign.response.logistics.LogisticsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Slf4j
@Component
public class LogisticsBusiness {

    @Autowired
    private ExWarehouseService exWarehouseService;

    @Autowired
    private ExWarehouseDetailService exWarehouseDetailService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private StorageService storageService;

    @Autowired
    private StorageSkuMapService storageSkuMapService;

    @Qualifier("redisPermanentTemplate")
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Value("${spring.profiles.active}")
    private String ACTIVE;

    @Autowired
    private LogisticsSkuService logisticsSkuService;

    @Autowired
    private LogisticsLogService logisticsLogService;

    @Autowired
    private SupplyGoodsService supplyGoodsService;

    @Autowired
    private TaskRecordService taskRecordService;

    @Autowired
    private StorageMapAddressBusiness storageMapAddressBusiness;

    @Autowired
    DeliveryConfig deliveryConfig;

    @Autowired
    QueueExpress queueExpress;
    @Qualifier("com.ets.delivery.application.app.thirdservice.feign.GoodsFeign")
    @Autowired
    private GoodsFeign goodsFeign;

    /*
     * 发货操作
     */
    public LogisticsAddOrderVO deliverOrder(LogisticsAddOrderDTO addOrderDTO) {
        if (!StorageCodeEnum.list.contains(addOrderDTO.getStorageCode())) {
            ToolsHelper.throwException("仓储编码不正确");
        }

        String lockKey = "LogisticsStatusLock:" + addOrderDTO.getLogisticsSn();
        String deliverOrderNo = null;

        try {
            // 发货单操作锁
            if (! ToolsHelper.addLock(redisTemplate, lockKey, 10)) {
                ToolsHelper.throwException("发货单操作中，请稍后！");
            }

            // 生成出库记录
            ExWarehouse exWarehouse = StorageFactory.create(addOrderDTO.getStorageCode()).initExWarehouse(addOrderDTO);

            // 生成出库单详情
            List<ExWarehouseDetail> detailList = StorageFactory.create(addOrderDTO.getStorageCode()).initExWarehouseDetail(addOrderDTO);

            if (ObjectUtils.isNotEmpty(exWarehouse)) {
                deliverOrderNo = exWarehouse.getDeliverOrderNo();
                if (StringUtils.isEmpty(deliverOrderNo)) {
                    // 下单发货
                    deliverOrderNo = StorageFactory.create(addOrderDTO.getStorageCode()).addOrder(exWarehouse, detailList);

                    // 更新出库记录
                    exWarehouse.setDeliverOrderNo(deliverOrderNo);
                    exWarehouse.setUpdatedAt(LocalDateTime.now());
                    exWarehouseService.updateById(exWarehouse);
                }
            }
        } catch (BizException e) {
            // 错误信息包含缺货
            if (StringUtils.isNotEmpty(e.getErrorMsg()) && e.getErrorMsg().contains("单据操作异常！商品库存不足下单失败!")) {
                // 使用正则表达式匹配商品编码
                String pattern = "\\(([^-]+)-";
                Pattern r = Pattern.compile(pattern);
                Matcher m = r.matcher(e.getErrorMsg());
                if (m.find()) {
                    String goodsCode = m.group(1);
                    ToolsHelper.throwException("仓库sku:" + goodsCode +" 库存不足", ErrCodeConstant.CODE_DELIVERY_STOCK_OUT.getCode());
                }
            }
            throw e;
        } catch (Throwable e) {
            ToolsHelper.throwException(e.getMessage());
        } finally {
            ToolsHelper.unLock(redisTemplate, lockKey);
        }

        LogisticsAddOrderVO addOrderVO = new LogisticsAddOrderVO();
        addOrderVO.setDeliverOrderNo(deliverOrderNo);
        return addOrderVO;
    }

    public void cancelOrder(LogisticsCancelOrderDTO cancelOrderDTO) {
        if (!StorageCodeEnum.list.contains(cancelOrderDTO.getStorageCode())) {
            ToolsHelper.throwException("仓储编码不正确");
        }

        ExWarehouse exWarehouse = exWarehouseService.getOneByIsvUuid(cancelOrderDTO.getLogisticsSn());
        if(ObjectUtils.isNotEmpty(exWarehouse) && StringUtils.isNotEmpty(exWarehouse.getDeliverOrderNo())) {
            StorageFactory.create(cancelOrderDTO.getStorageCode()).cancelOrder(cancelOrderDTO);
        }
    }

    public void cancelLogistics(LogisticsCancelDTO cancelDTO) {
        Logistics logistics = logisticsService.getById(cancelDTO.getLogisticsId());
        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException("发货单不存在");
        }

        // 发货单已取消
        if (logistics.getStatus().equals(LogisticsStatusEnum.STATUS_CANCEL.getValue()) ||
            logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_CANCEL.getValue())
        ) {
            return;
        }

        // 已发货不能取消
        if (logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue())) {
            ToolsHelper.throwException("已发货订单不可取消");
        }

        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        String lockKey = "LogisticsStatusLock:" + logistics.getLogisticsSn();
        try {
            // 发货单操作锁
            if (!ToolsHelper.addLock(redisTemplate, lockKey, 10)) {
                ToolsHelper.throwException("发货单操作中，请稍后！");
            }

            // 请求运营商取消发货
            LogisticsCancelOrderDTO cancelOrderDTO = new LogisticsCancelOrderDTO();
            cancelOrderDTO.setLogisticsSn(logistics.getLogisticsSn());
            cancelOrderDTO.setStorageCode(logistics.getStorageCode());
            cancelOrder(cancelOrderDTO);

            // 更新状态
            logistics.setStatus(LogisticsStatusEnum.STATUS_CANCEL.getValue());
            logistics.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_CANCEL.getValue());
            logisticsService.updateById(logistics);

            // 记录日志
            logisticsLogService.addLog(logistics.getId(),
                    LogisticsOperateTypeEnum.TYPE_CANCEL.getCode(),
                    "取消发货成功",
                    user.getUsername());
        } catch (Throwable e) {
            logisticsLogService.addLog(logistics.getId(),
                    LogisticsOperateTypeEnum.TYPE_CANCEL.getCode(),
                    "取消发货失败：" + e.getMessage(),
                    user.getUsername());
            ToolsHelper.throwException(e.getMessage());
        } finally {
            ToolsHelper.unLock(redisTemplate, lockKey);
        }
    }

    public void orderConfirm(LogisticsOrderConfirmDTO confirmDTO) {
        if (!StorageCodeEnum.list.contains(confirmDTO.getStorageCode())) {
            ToolsHelper.throwException("仓储编码不正确");
        }

        if (confirmDTO.getOrderSource() == 2) {
            // erp订单
            StorageFactory.create(confirmDTO.getStorageCode()).erpOrderConfirm(confirmDTO);
        } else {
            StorageFactory.create(confirmDTO.getStorageCode()).orderConfirm(confirmDTO);
        }
    }

    public void orderProcess(LogisticsOrderProcessDTO processDTO) {
        if (!StorageCodeEnum.list.contains(processDTO.getStorageCode())) {
            ToolsHelper.throwException("仓储编码不正确");
        }
        queueExpress.push(new LogisticsOrderProcessDisposer(processDTO));
    }

    public Double getLogisticsAverage(String goodsCode, Integer avgDay, Integer beforeDay) {
        // 统计平均发货量
        LocalDateTime endDate = LocalDateTimeUtil.endOfDay(LocalDateTime.now().minusDays(1 + beforeDay));
        LocalDateTime startDate = LocalDateTimeUtil.beginOfDay(endDate.minusDays(avgDay - 1));
        Logistics logistics = logisticsService.getLogisticsSumByGoodsCode(goodsCode, startDate, endDate);
        double quantity = 0.0;
        if (ObjectUtil.isNotNull(logistics)) {
            quantity = logistics.getSumGoodsNums() / avgDay;
        }
        return quantity;
    }

    public Map<String, Double> getLogisticsAverageMap(String storageCode, List<String> goodsCodeList, Integer avgDay, Integer beforeDay) {
        // 统计总发货量
        LocalDateTime endDate = LocalDateTimeUtil.endOfDay(LocalDateTime.now().minusDays(1 + beforeDay));
        LocalDateTime startDate = LocalDateTimeUtil.beginOfDay(endDate.minusDays(avgDay - 1));
        Map<String, Double> map = getLogisticsSumMapByDateRange(storageCode, goodsCodeList, startDate, endDate);

        // 计算平均发货量
        Map<String, Double> avgMap = new HashMap<>();
        map.entrySet().stream()
                .filter(entry -> ObjectUtil.isNotEmpty(entry.getValue()))
                .forEach(entry -> {
                    double quantity = entry.getValue() / avgDay;
                    avgMap.put(entry.getKey(), quantity);
                });

        return avgMap;
    }

    public Map<String, Double> getLogisticsSumMap(String storageCode, List<String> goodsCodeList, Integer sumDay) {
        // 统计总发货量
        LocalDateTime endDate = LocalDateTimeUtil.endOfDay(LocalDateTime.now().minusDays(1));
        LocalDateTime startDate = LocalDateTimeUtil.beginOfDay(endDate.minusDays(sumDay - 1));
        return getLogisticsSumMapByDateRange(storageCode, goodsCodeList, startDate, endDate);
    }

    /**
     * 根据日期范围统计发货量
     * @param goodsCodeList 商品列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return Map<goodsCode, sumGoodsNums>
     */
    public Map<String, Double> getLogisticsSumMapByDateRange(String storageCode, List<String> goodsCodeList, LocalDateTime startDate, LocalDateTime endDate) {
        Map<String, Double> map = new HashMap<>();
        // 获取发货单统计数据
        List<Logistics> logisticsList = logisticsService.getLogisticsSumByGoodsCodeList(storageCode, goodsCodeList, startDate, endDate);
        if (ObjectUtils.isNotEmpty(logisticsList)) {
            logisticsList.stream()
                    .filter(ObjectUtil::isNotEmpty)
                    .forEach(logistics -> {
                        // 同一个goodsCode多个分片
                        Double sum = map.get(logistics.getGoodsCode());
                        if (ObjectUtils.isNotEmpty(sum)) {
                            sum += logistics.getSumGoodsNums();
                            map.remove(logistics.getGoodsCode());
                        } else {
                            sum = logistics.getSumGoodsNums();
                        }
                        map.put(logistics.getGoodsCode(), sum);
                    });
        }

        // 获取发货单记录
        List<Logistics> logisticsSkuList = logisticsService.getLogisticsSnListByDeliveryTime(storageCode, startDate, endDate);
        if (ObjectUtils.isEmpty(logisticsSkuList)) {
            return map;
        }
        List<String> logisticsSnList = logisticsSkuList.stream()
                .map(Logistics::getLogisticsSn)
                .collect(Collectors.toList());

        // 获取发货sku统计数据
        List<LogisticsSku> storageSkuSumList = logisticsSkuService.getLogisticsSumByStorageSkuList(goodsCodeList, logisticsSnList);
        storageSkuSumList.stream()
                .filter(ObjectUtil::isNotEmpty)
                .forEach(logistics -> {
                    // 合并sku发货数量
                    Double sum = map.get(logistics.getStorageSku());
                    if (ObjectUtils.isNotEmpty(sum)) {
                        sum += logistics.getSumGoodsNums();
                        map.remove(logistics.getStorageSku());
                    } else {
                        sum = logistics.getSumGoodsNums();
                    }
                    map.put(logistics.getStorageSku(), sum);
                });
        return map;
    }

    public boolean isDevOrTest() {
        return Arrays.asList("dev","dev1","test","test1").contains(ACTIVE);
    }

    /*
     *  接收发货订单
     */
    public AcceptVO accept(LogisticsAcceptDTO logisticsAcceptDTO) {

        if (isDevOrTest() || logisticsAcceptDTO.getStorageCode().equals("Normal")) {
            // 开发测试支持手动发货操作
            logisticsAcceptDTO.setOperator("");
        }

        try{
            //校验收货地址格式
            String[] sendAreas = logisticsAcceptDTO.getSendArea().trim().split("\\s+");
            if(sendAreas.length != 3){
                ToolsHelper.throwException("收件地区格式不正确（省 市 区）");
            }
            //去掉地址空格、换行
            logisticsAcceptDTO.setSendAddress(logisticsAcceptDTO.getSendAddress().replaceAll("[\\s*\\u00A0]", ""));
            //判断仓库是否存在，传空表示为默认仓库，传normal表示为手动发货
            if(StringUtils.isEmpty(logisticsAcceptDTO.getStorageCode())){
                // 根据地址映射仓库
                AddressMapInfoVO addressMapInfoVO = storageMapAddressBusiness.addressMapStorageCode(logisticsAcceptDTO.getSendArea(), logisticsAcceptDTO.getAddressConfigId());
                logisticsAcceptDTO.setStorageCode(addressMapInfoVO.getStorageCode());
                //如果指定LogisticsCode才设置
                if(!StringUtils.isEmpty(addressMapInfoVO.getLogisticsCode())){
                    logisticsAcceptDTO.setLogisticsCode(addressMapInfoVO.getLogisticsCode());
                }

                if(storageService.getOneByStorageCode(logisticsAcceptDTO.getStorageCode()) == null){
                    ToolsHelper.throwException("仓库"+logisticsAcceptDTO.getStorageCode()+"不存在");
                }
            }

            //韵达物流是否默认物流方式
            if(logisticsAcceptDTO.getStorageCode().equals(StorageCodeConstant.YUNDA) && StringUtils.isEmpty(logisticsAcceptDTO.getLogisticsCode())){
                logisticsAcceptDTO.setLogisticsCode(deliveryConfig.getDefaultExpressCorpCode());
            }
            //判断物流方式是否存在,传空表示默认物流方式
            if( ExpressCorpEnum.getDescByCode(logisticsAcceptDTO.getLogisticsCode())== null){
                ToolsHelper.throwException("物流方式"+logisticsAcceptDTO.getLogisticsCode()+"不存在");
            }
            // 判断sku是否存在
            List<String> skuArr = logisticsAcceptDTO.getSkuList().stream().map(LogisticsAcceptDTO.Sku::getSku).collect(Collectors.toList());
            List<StorageSkuMap> storageSkuMapList = storageSkuMapService.getListByStorageCodeSku(logisticsAcceptDTO.getStorageCode(),skuArr);
            List<String> compareSkuArr = storageSkuMapList.stream().map(StorageSkuMap::getSku).collect(Collectors.toList());
            //差集
            skuArr.removeAll(compareSkuArr);
            if(!skuArr.isEmpty()){
                ToolsHelper.throwException("仓库"+logisticsAcceptDTO.getStorageCode()+"不存在sku:"+skuArr);
            }
            // 补充发货商品名称
            Map<String, String> skuNameMap = storageSkuMapList.stream().collect(Collectors.toMap(StorageSkuMap::getSku, StorageSkuMap::getGoodsName));
            Map<String, String> skuStorageSkuMap = storageSkuMapList.stream().collect(Collectors.toMap(StorageSkuMap::getSku, StorageSkuMap::getStorageSku));
            logisticsAcceptDTO.getSkuList().forEach(sku -> {
                if (StringUtils.isEmpty(sku.getGoodsName())) {
                    sku.setGoodsName(skuNameMap.getOrDefault(sku.getSku(), ""));
                }
                if (StringUtils.isEmpty(sku.getStorageSku())) {
                    sku.setStorageSku(skuStorageSkuMap.getOrDefault(sku.getSku(), ""));
                }
            });

            //加锁
            if (! ToolsHelper.addLock(redisTemplate, "accept:"+logisticsAcceptDTO.getOrderSn(), 20)) {
                ToolsHelper.throwException("发货单处理中，请稍后！");
            }

            //创建发货单，发货商品数据单
            String logisticsSn = ToolsHelper.genNum(redisTemplate,"etc_logistics",ACTIVE,8);
            Logistics logistics = logisticsService.createLogistics(logisticsSn,logisticsAcceptDTO);
            logisticsSkuService.create(logisticsSn,logisticsAcceptDTO);

            //添加日志
            logisticsLogService.addLog(logistics.getId(), LogisticsOperateTypeEnum.TYPE_ADD.getCode(),"新增发货订单"+  logisticsSn,"system");

            //塞队列进行发货操作
            TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
            taskRecordDTO.setReferSn(logistics.getLogisticsSn());
            taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_LOGISTICS_DELIVER_GOODS.getType());
            TaskFactory.create(TaskRecordReferTypeConstant.LOGISTICS_DELIVER_GOODS).addAndPush(taskRecordDTO);

            //返回物流单号
            AcceptVO acceptVO = new AcceptVO();
            acceptVO.setLogisticsSn(logisticsSn);
            return acceptVO;
        }catch (Exception e) {
            log.error(logisticsAcceptDTO.toString());
            e.printStackTrace();
            ToolsHelper.throwException("接收发货订单异常:"+e.getMessage());
            return null;
        }
    }

    /*
     *  接收发货订单
     */
    public AttemptCancelVO attemptCancel(String logisticsSn) throws BizException{

        AttemptCancelVO attemptCancelVO = new AttemptCancelVO();
        try{
            Logistics logistics = logisticsService.getByLogisticsSn(logisticsSn);

            if (ObjectUtils.isEmpty(logistics)) {
                ToolsHelper.throwException("发货单不存在");
            }

            // 已取消
            if (logistics.getStatus().equals(LogisticsStatusEnum.STATUS_CANCEL.getValue())) {
                attemptCancelVO.setStatus(1);
                return attemptCancelVO;
            }

            // 已发货不能取消
            if (logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue())) {
                ToolsHelper.throwException("已发货订单不可取消");
            }

            //加锁
            if (! ToolsHelper.addLock(redisTemplate, "attemptCancel:"+logisticsSn, 20)) {
                ToolsHelper.throwException("发货单尝试取消中，请稍后！");
            }
            //还没有发货的
            ExWarehouse exWarehouse = exWarehouseService.getOneByIsvUuid(logisticsSn);
            if(!exWarehouse.getDeliverOrderNo().isEmpty()){
                //尝试取消发货单
                if (!StorageCodeEnum.list.contains(logistics.getStorageCode())) {
                    ToolsHelper.throwException("仓储编码不正确");
                }

                LogisticsCancelOrderDTO logisticsCancelOrderDTO = new LogisticsCancelOrderDTO();
                logisticsCancelOrderDTO.setLogisticsSn(logistics.getLogisticsSn());
                logisticsCancelOrderDTO.setStorageCode(logistics.getStorageCode());
                //取消发货单
                StorageFactory.create(logistics.getStorageCode()).cancelOrder(logisticsCancelOrderDTO);
            }

            // 修改发货单状态
            logistics.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_CANCEL.getValue());
            logistics.setStatus(LogisticsStatusEnum.STATUS_CANCEL.getValue());
            logistics.setUpdatedAt(LocalDateTime.now());
            logisticsService.updateById(logistics);

            // 记录取消日志
            logisticsLogService.addLog(
                    logistics.getId(),
                    LogisticsOperateTypeEnum.TYPE_CANCEL.getCode(),
                    "取消发货" + logistics.getLogisticsSn() + "，原因：用户取消",
                    "system"
            );

            attemptCancelVO.setStatus(1);

        }catch (Exception e) {
            attemptCancelVO.setStatus(2);
            attemptCancelVO.setMsg("接收发货订单异常:"+e.getMessage());
        }

        return attemptCancelVO;
    }

    public void cancelByOrderSn(LogisticsCancelByOrderSnDTO cancelByOrderSnDTO) {
        // 获取发货单
        Logistics logistics = logisticsService.getLatestOneByOrderSn(cancelByOrderSnDTO.getOrderSn());

        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException("发货单不存在");
        }

        // 已取消
        if (logistics.getStatus().equals(LogisticsStatusEnum.STATUS_CANCEL.getValue())) {
            return;
        }

        // 已发货不能取消
        if (logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue())) {
            ToolsHelper.throwException("已发货订单不可取消");
        }

        String lockKey = "LogisticsStatusLock:" + logistics.getLogisticsSn();
        try {
            // 加锁
            if (! ToolsHelper.addLock(redisTemplate, lockKey, 10)) {
                ToolsHelper.throwException("发货单操作中，请稍后！");
            }

            // 已创建出库单 未下单成功
            ExWarehouse exWarehouse = exWarehouseService.getOneByIsvUuid(logistics.getLogisticsSn());
            if(ObjectUtils.isNotEmpty(exWarehouse) && StringUtils.isNotEmpty(exWarehouse.getDeliverOrderNo())){
                if (!StorageCodeEnum.list.contains(logistics.getStorageCode())) {
                    ToolsHelper.throwException("仓储编码不正确");
                }

                LogisticsCancelOrderDTO logisticsCancelOrderDTO = new LogisticsCancelOrderDTO();
                logisticsCancelOrderDTO.setLogisticsSn(logistics.getLogisticsSn());
                logisticsCancelOrderDTO.setStorageCode(logistics.getStorageCode());
                // 取消发货单
                StorageFactory.create(logistics.getStorageCode()).cancelOrder(logisticsCancelOrderDTO);
            }

            // 修改发货单状态
            logistics.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_CANCEL.getValue());
            logistics.setStatus(LogisticsStatusEnum.STATUS_CANCEL.getValue());
            logistics.setUpdatedAt(LocalDateTime.now());
            logisticsService.updateById(logistics);

            // 记录取消日志
            logisticsLogService.addLog(
                    logistics.getId(),
                    LogisticsOperateTypeEnum.TYPE_CANCEL.getCode(),
                    "取消发货" + logistics.getLogisticsSn() + "，原因：" + cancelByOrderSnDTO.getReason(),
                    "system"
            );
        } catch (Throwable e) {
            String msg = "取消发货操作失败：" + e.getMessage();
            log.error(msg);
            ToolsHelper.throwException(msg);
        } finally {
            // 解锁
            ToolsHelper.unLock(redisTemplate, lockKey);
        }
    }

    /**
     * 通过订单号获取发货单信息
     */
    public LogisticsVO findByOrderSn(LogisticsFindByOrderSnDTO logisticsFindByOrderSnDTO) {
        Logistics logistics = logisticsService.getLatestOneByOrderSn(logisticsFindByOrderSnDTO.getOrderSn());

        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException("发货单不存在");
        }
        return BeanUtil.copyProperties(logistics, LogisticsVO.class);
    }

    /**
     * 通过快递单号查询发货单的SKU数量总额
     *
     * @param expressNumber 快递单号
     * @return SKU数量总额信息
     */
    public LogisticsSkuSummaryVO getSkuSummaryByExpressNumber(String expressNumber) {
        // 根据快递单号查询发货单
        Logistics logistics = logisticsService.getByExpressNumber(expressNumber);
        if (ObjectUtils.isEmpty(logistics)) {
            log.warn("未找到该快递单号对应的发货单：{}", expressNumber);
            return null;
        }

        // 构建返回结果
        LogisticsSkuSummaryVO summaryVO = new LogisticsSkuSummaryVO();
        summaryVO.setLogisticsSn(logistics.getLogisticsSn());
        summaryVO.setExpressNumber(expressNumber);

        // 兼容历史旧发货单没有sku表记录 只有一个sku
        if (StringUtils.isNotBlank(logistics.getGoodsCode())) {
            summaryVO.setTotalSkuCount(logistics.getNums());
            List<LogisticsSkuSummaryVO.SkuDetailVO> skuDetails = new ArrayList<>();
            LogisticsSkuSummaryVO.SkuDetailVO detailVO = new LogisticsSkuSummaryVO.SkuDetailVO();
            detailVO.setStorageSku(logistics.getGoodsCode());
            detailVO.setNums(logistics.getNums());
            skuDetails.add(detailVO);
            summaryVO.setSkuDetails(skuDetails);
        } else {
            // 根据发货单流水号查询SKU列表
            List<LogisticsSku> logisticsSkuList = logisticsSkuService.getListByLogisticsSn(logistics.getLogisticsSn());
            if (ObjectUtils.isEmpty(logisticsSkuList)) {
                log.warn("发货单{}没有SKU信息", logistics.getLogisticsSn());
                return null;
            }

            try {
                // 查询sku信息 过滤非ETC设备
                // 先获取所有sku列表
                List<String> skuList = logisticsSkuList.stream().map(LogisticsSku::getSku).toList();
                // 查询sku信息
                GoodsSkuInfoDTO goodsSkuInfoDTO = new GoodsSkuInfoDTO();
                goodsSkuInfoDTO.setSkuSns(skuList);
                goodsSkuInfoDTO.setWithGoods(true);
                String skusInfo = goodsFeign.getSkusInfo(goodsSkuInfoDTO);

                JsonResult<JSONArray> jsonResult = JsonResult.convertFromJsonStr(skusInfo, JSONArray.class);
                jsonResult.checkError();
                JSONArray data = jsonResult.getData();
                data.forEach(item -> {
                    JSONObject jsonObject = (JSONObject) item;
                    String skuSn = jsonObject.getString("skuSn");
                    JSONObject goodsEntity = jsonObject.getJSONObject("goodsEntity");
                    if (goodsEntity != null) {
                        String extra = goodsEntity.getString("extra");
                        if (StringUtils.isNotEmpty(extra)) {
                            // extra = {"etcType":"card_obu"}
                            JSONObject extraObj = JSON.parseObject(extra);
                            if (!extraObj.containsValue("card_obu")) {
                                // 过滤非ETC设备
                                logisticsSkuList.removeIf(sku -> sku.getSku().equals(skuSn));
                            }
                        }
                    }
                });
            } catch (Exception e) {
                log.warn("判断sku是否ETC设备异常:" + e.getMessage());
            }

            if (ObjectUtils.isEmpty(logisticsSkuList)) {
                log.warn("发货单{}没有ETC设备SKU信息", logistics.getLogisticsSn());
                return null;
            }

            // 计算SKU总数量
            Integer totalSkuCount = logisticsSkuList.stream()
                    .mapToInt(LogisticsSku::getNums)
                    .sum();
            summaryVO.setTotalSkuCount(totalSkuCount);

            // 构建SKU详细信息列表
            List<LogisticsSkuSummaryVO.SkuDetailVO> skuDetails = logisticsSkuList.stream()
                    .map(sku -> {
                        LogisticsSkuSummaryVO.SkuDetailVO detailVO = new LogisticsSkuSummaryVO.SkuDetailVO();
                        detailVO.setSku(sku.getSku());
                        detailVO.setGoodsName(sku.getGoodsName());
                        detailVO.setStorageSku(sku.getStorageSku());
                        detailVO.setNums(sku.getNums());
                        return detailVO;
                    })
                    .collect(Collectors.toList());
            summaryVO.setSkuDetails(skuDetails);
        }

        return summaryVO;
    }

    public Logistics orderQuery(ExWarehouse exWarehouse) {
        Logistics logistics = logisticsService.getByLogisticsSn(exWarehouse.getIsvUuid());
        if (ObjectUtils.isEmpty(logistics)) {
            return null;
        }

        // 已取消
        if (logistics.getStatus().equals(LogisticsStatusEnum.STATUS_CANCEL.getValue())) {
            return null;
        }

        ExWarehouse afterExWarehouse = StorageFactory.create(logistics.getStorageCode()).orderQuery(exWarehouse);
        if (ObjectUtils.isNotEmpty(afterExWarehouse) && ObjectUtils.isEmpty(logistics.getExpressNumber())) {
            String expressNumber = "";
            // 出库单是否拆单
            if (afterExWarehouse.getSplitFlag() == 1) {
                // 获取拆单记录
                List<ExWarehouse> splitOrderList = exWarehouseService.getSplitOrderList(afterExWarehouse.getDeliverOrderNo());
                if (ObjectUtils.isNotEmpty(splitOrderList)) {
                    // 临时取第一个运单号
                    for (ExWarehouse splitOrder: splitOrderList) {
                        if (StringUtils.isNotEmpty(splitOrder.getWayBill())) {
                            expressNumber = splitOrder.getWayBill();
                        }
                    }
                }
            } else {
                expressNumber = afterExWarehouse.getWayBill();
            }

            // 更新发货单
            logistics.setExpressCorp("京东物流");
            logistics.setExpressNumber(expressNumber);
            logistics.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue());
            logistics.setDeliveryTime(LocalDateTime.now());
            logistics.setDeliveryRemark("京东发货成功");
            logistics.setNotifyStatus(LogisticsNotifyStatusEnum.NOTIFY_STATUS_PROCESSING.getValue());
            logistics.setNotifyTime(null);
            logisticsService.updateById(logistics);

            // 记录日志
            logisticsLogService.addLog(logistics.getId(),
                    LogisticsOperateTypeEnum.TYPE_DELIVERY.getCode(),
                    "京东物流单号：" + expressNumber,
                    "system");
        }

        return logistics;
    }
    public IPage<LogisticsListVO> getList(LogisticsListDTO listDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        LogisticsPageBO logisticsPageBO = BeanUtil.copyProperties(listDTO, LogisticsPageBO.class,
                "createStartTime", "createEndTime", "deliveryStartTime", "deliveryEndTime");
        // 发货流水号处理
        if (StringUtils.isNotEmpty(listDTO.getLogisticsSn())) {
            logisticsPageBO.setLogisticsSnList(Collections.singletonList(listDTO.getLogisticsSn()));
        }

        if (StringUtils.isNotEmpty(listDTO.getSendPhone())) {
            logisticsPageBO.setSendPhone(listDTO.getSendPhone());
        }

        // 创建时间处理
        if (ObjectUtils.isNotEmpty(listDTO.getCreateStartTime())) {
            logisticsPageBO.setCreateStartTime(listDTO.getCreateStartTime().atStartOfDay());
        }
        if (ObjectUtils.isNotEmpty(listDTO.getCreateEndTime())) {
            logisticsPageBO.setCreateEndTime(listDTO.getCreateEndTime().atTime(LocalTime.MAX));
        }

        // 发货时间处理
        if (ObjectUtils.isNotEmpty(listDTO.getDeliveryStartTime())) {
            logisticsPageBO.setDeliveryStartTime(listDTO.getDeliveryStartTime().atStartOfDay());
        }
        if (ObjectUtils.isNotEmpty(listDTO.getDeliveryEndTime())) {
            logisticsPageBO.setDeliveryEndTime(listDTO.getDeliveryEndTime().atTime(LocalTime.MAX));
        }

        // 查询商品 需要带创建时间或者发货时间 时间范围30天
        if (ObjectUtils.isNotEmpty(listDTO.getGoodsUnionCode())) {
            if ((ObjectUtils.isEmpty(logisticsPageBO.getCreateStartTime()) && ObjectUtils.isEmpty(logisticsPageBO.getCreateEndTime()))
                &&
                (ObjectUtils.isEmpty(logisticsPageBO.getDeliveryStartTime()) && ObjectUtils.isEmpty(logisticsPageBO.getDeliveryEndTime()))) {
                ToolsHelper.throwException("查询商品需要带创建时间或者发货时间");
            }

            if (ObjectUtils.isNotEmpty(logisticsPageBO.getCreateStartTime()) && ObjectUtils.isNotEmpty(logisticsPageBO.getCreateEndTime())) {
                if (logisticsPageBO.getCreateStartTime().plusDays(30).isBefore(logisticsPageBO.getCreateEndTime())) {
                    ToolsHelper.throwException("查询商品创建时间范围不能超过30天");
                }
            }

            if (ObjectUtils.isNotEmpty(logisticsPageBO.getDeliveryStartTime()) && ObjectUtils.isNotEmpty(logisticsPageBO.getDeliveryEndTime())) {
                if (logisticsPageBO.getDeliveryStartTime().plusDays(30).isBefore(logisticsPageBO.getDeliveryEndTime())) {
                    ToolsHelper.throwException("查询商品发货时间范围不能超过30天");
                }
            }

            // 没有创建开始时间
            if (ObjectUtils.isEmpty(logisticsPageBO.getCreateStartTime()) && ObjectUtils.isNotEmpty(logisticsPageBO.getCreateEndTime()) &&
                    (ObjectUtils.isEmpty(logisticsPageBO.getDeliveryStartTime()) && ObjectUtils.isEmpty(logisticsPageBO.getDeliveryEndTime()))) {
                ToolsHelper.throwException("查询商品需要带创建时间或者发货时间");
            }

            // 没有创建结束时间
            if (ObjectUtils.isNotEmpty(logisticsPageBO.getCreateStartTime()) && ObjectUtils.isEmpty(logisticsPageBO.getCreateEndTime()) &&
                    (ObjectUtils.isEmpty(logisticsPageBO.getDeliveryStartTime()) && ObjectUtils.isEmpty(logisticsPageBO.getDeliveryEndTime()))) {
                ToolsHelper.throwException("查询商品需要带创建时间或者发货时间");
            }

            // 没有发货开始时间
            if (ObjectUtils.isEmpty(logisticsPageBO.getDeliveryStartTime()) && ObjectUtils.isNotEmpty(logisticsPageBO.getDeliveryEndTime()) &&
                    (ObjectUtils.isEmpty(logisticsPageBO.getCreateStartTime()) && ObjectUtils.isEmpty(logisticsPageBO.getCreateEndTime()))) {
                ToolsHelper.throwException("查询商品需要带创建时间或者发货时间");
            }

            // 没有发货结束时间
            if (ObjectUtils.isNotEmpty(logisticsPageBO.getDeliveryStartTime()) && ObjectUtils.isEmpty(logisticsPageBO.getDeliveryEndTime()) &&
                    (ObjectUtils.isEmpty(logisticsPageBO.getCreateStartTime()) && ObjectUtils.isEmpty(logisticsPageBO.getCreateEndTime()))) {
                ToolsHelper.throwException("查询商品需要带创建时间或者发货时间");
            }
        }

        // 过滤手动下单
        List<Logistics> manualLogisticsSaveList = logisticsService.getManualLogisticsSaveList();
        if (ObjectUtils.isNotEmpty(manualLogisticsSaveList)) {
            List<String> manualLogisticsSnList = manualLogisticsSaveList.stream().map(Logistics::getLogisticsSn).collect(Collectors.toList());
            logisticsPageBO.setNotLogisticsSnList(manualLogisticsSnList);
        }

        if (ObjectUtils.isNotEmpty(user) && StringUtils.isNotEmpty(user.getStorageCode())) {
            String[] storageCodeSplit = user.getStorageCode().split(",");
            logisticsPageBO.setStorageCodeList(Arrays.asList(storageCodeSplit));// 选中的仓库没有权限
            if (StringUtils.isNotEmpty(listDTO.getStorageCode()))
                if (!logisticsPageBO.getStorageCodeList().contains(listDTO.getStorageCode())) {
                    return new Page<>();
                } else {
                    logisticsPageBO.setStorageCodeList(Collections.singletonList(listDTO.getStorageCode()));
                }
        }

        // 商品编码处理
        if (StringUtils.isNotEmpty(listDTO.getGoodsUnionCode())) {
            // 统一编码查找仓库sku
            List<SupplyGoods> supplyGoodsList = supplyGoodsService.getListByGoodsUnionCode(listDTO.getGoodsUnionCode());
            if (ObjectUtils.isEmpty(supplyGoodsList)) {
                return new Page<>();
            }
            // 仓库sku查找商品sku
            List<String> goodsCodeList = supplyGoodsList.stream().map(SupplyGoods::getGoodsCode).distinct().collect(Collectors.toList());
            logisticsPageBO.setStorageSkuList(goodsCodeList);
        }

        switch (listDTO.getType()) {
            case 1:
                // 发货汇总
                break;
            case 2:
                // 待发货
                logisticsPageBO.setOperatorList(Arrays.asList("", user.getUsername()));
                logisticsPageBO.setDeliveryStatusList(Arrays.asList(
                        LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue(),
                        LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue())
                );
                logisticsPageBO.setStatus(LogisticsStatusEnum.STATUS_NORMAL.getValue());
                break;
            default:
                ToolsHelper.throwException("当前type类型尚未支持");
                break;
        }

        IPage<Logistics> logisticsPage = logisticsService.getPage(logisticsPageBO);
        return logisticsPage.convert(logistics -> {
            LogisticsListVO logisticsListVO = BeanUtil.copyProperties(logistics, LogisticsListVO.class);
            logisticsListVO.setIssuerIdStr(deliveryConfig.getCnNameByIssuerId(logistics.getIssuerId()));
            logisticsListVO.setPlateNo(DesensitizedUtil.carLicense(logistics.getPlateNo()));
            logisticsListVO.setSendName(StrUtil.hide(logistics.getSendName(), 1, 2));
            logisticsListVO.setSendAddress("****");
            logisticsListVO.setSendPhone(DesensitizedUtil.mobilePhone(logistics.getSendPhone()));
            logisticsListVO.setGoodsList(getLogisticsGoodsList(logistics));

            if (StringUtils.isNotEmpty(logistics.getOriginBusinessSn())) {
                logisticsListVO.setOrderSn(logistics.getOrderSn() + "\n(" + logistics.getOriginBusinessSn() + ")");
            }

            if (isDevOrTest()) {
                logisticsListVO.setShowManualDeliveryButton(logistics.getStatus().equals(LogisticsStatusEnum.STATUS_NORMAL.getValue()) &&
                        ObjectUtils.isNotEmpty(user) &&
                        user.getUsername().equals(logistics.getOperator()) &&
                        Arrays.asList(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue(),
                                LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue()).contains(logistics.getDeliveryStatus()));
                logisticsListVO.setShowManualAllowGetButton(
                        logistics.getStatus().equals(LogisticsStatusEnum.STATUS_NORMAL.getValue()) &&
                                StringUtils.isEmpty(logistics.getOperator()) &&
                        Arrays.asList(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue(),
                                LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue()).contains(logistics.getDeliveryStatus())
                );
            }

            return logisticsListVO;
        });
    }

    public LogisticsDetailVO getDetail(LogisticsDetailDTO detailDTO) {
        Logistics logistics = logisticsService.getById(detailDTO.getLogisticsId());
        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException("发货单不存在");
        }

        LogisticsDetailVO logisticsDetailVO = new LogisticsDetailVO();
        LogisticsDetailVO.ReceiveInfo receiveInfo = BeanUtil.copyProperties(logistics, LogisticsDetailVO.ReceiveInfo.class);
        LogisticsDetailVO.DeliveryInfo deliveryInfo = BeanUtil.copyProperties(logistics, LogisticsDetailVO.DeliveryInfo.class);
        LogisticsDetailVO.GoodsInfo goodsInfo = BeanUtil.copyProperties(logistics, LogisticsDetailVO.GoodsInfo.class);
        goodsInfo.setIssuerIdStr(deliveryConfig.getCnNameByIssuerId(logistics.getIssuerId()));
        goodsInfo.setGoodsList(getLogisticsGoodsList(logistics));

        logisticsDetailVO.setReceiveInfo(receiveInfo);
        logisticsDetailVO.setDeliveryInfo(deliveryInfo);
        logisticsDetailVO.setGoodsInfo(goodsInfo);
        return logisticsDetailVO;
    }

    public IPage<LogisticsLogVO> getLog(LogisticsLogDTO logDTO) {
        IPage<LogisticsLog> page = logisticsLogService.getPage(logDTO.getLogisticsId(), logDTO.getPageNum(), logDTO.getPageSize());
        return page.convert(log -> BeanUtil.copyProperties(log, LogisticsLogVO.class));
    }

    /**
     * 统计指定天数内的缺货SKU
     * @param days 统计天数
     * @return SKU和缺货次数的映射，按缺货次数降序排序
     */
    public List<Map.Entry<String, Map<String, Object>>> getStockOutSkuStats(int days) {
        LocalDateTime startTime = LocalDateTime.now().minusDays(days);
        
        // 查询缺货订单
        List<Logistics> stockOutLogistics = logisticsService.getStockOutLogistics(
            "仓库缺货", 
            LogisticsStatusEnum.STATUS_NORMAL.getValue(),
            startTime
        );
        
        if (ObjectUtils.isEmpty(stockOutLogistics)) {
            return new ArrayList<>();
        }

        // 获取所有SKU
        List<String> logisticsSns = stockOutLogistics.stream()
            .map(Logistics::getLogisticsSn)
            .collect(Collectors.toList());
        
        List<LogisticsSku> skuList = logisticsSkuService.getListByLogisticsSns(logisticsSns);
        
        // 统计SKU缺货次数和名称
        Map<String, Map<String, Object>> skuStats = skuList.stream()
            .collect(Collectors.groupingBy(
                LogisticsSku::getStorageSku,
                Collectors.collectingAndThen(
                    Collectors.toList(),
                    list -> {
                        Map<String, Object> stats = new HashMap<>();
                        stats.put("count", (long) list.size());
                        stats.put("name", list.get(0).getGoodsName());
                        return stats;
                    }
                )
            ));
        
        // 按缺货次数降序排序
        List<Map.Entry<String, Map<String, Object>>> sortedSkuList = new ArrayList<>(skuStats.entrySet());
        sortedSkuList.sort((a, b) -> ((Long)b.getValue().get("count")).compareTo((Long)a.getValue().get("count")));
        
        return sortedSkuList;
    }

    public Map<String, Object> menuList() {
        Map<String, Object> map = new HashMap<>();
        // 商品统一编码列表
        List<SupplyGoods> supplyGoodsList = supplyGoodsService.getGoodsUnionCodeList();
        List<Map<String, String>> goodsUnionCodeList = supplyGoodsList.stream()
                .map(goods -> {
                    Map<String, String> goodsUnionCodeMap = new HashMap<>();
                    goodsUnionCodeMap.put("goods_union_code", goods.getGoodsUnionCode());
                    goodsUnionCodeMap.put("goods_name", goods.getGoodsName());
                    return goodsUnionCodeMap;
                })
                .collect(Collectors.toList());
        map.put("goods_union_code_list", goodsUnionCodeList);
        return map;
    }

    public void receiveLogistics(LogisticsReceiveDTO receiveDTO) {
        LogisticsOneBO oneBO = new LogisticsOneBO();
        oneBO.setId(receiveDTO.getLogisticsId());
        oneBO.setOperator("");
        oneBO.setStatus(LogisticsStatusEnum.STATUS_NORMAL.getValue());
        oneBO.setDeliveryStatusList(Arrays.asList(
                LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue(),
                LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue()));
        Logistics logistics = logisticsService.getOneByCondition(oneBO);
        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException("发货单领取失败：发货单无效或者已被领取");
        }

        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        logistics.setOperator(user.getUsername());
        logistics.setDrawTime(LocalDateTime.now());
        logistics.setUpdatedAt(LocalDateTime.now());
        logisticsService.updateById(logistics);

        // 记录日志
        logisticsLogService.addLog(logistics.getId(),
                LogisticsOperateTypeEnum.TYPE_RECEIVE.getCode(),
                "发货单已被" + user.getUsername() + "领取",
                user.getUsername());
    }

    public void cancelReceiveLogistics(LogisticsReceiveDTO receiveDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        LogisticsOneBO oneBO = new LogisticsOneBO();
        oneBO.setId(receiveDTO.getLogisticsId());
        oneBO.setOperator(user.getUsername());
        oneBO.setStatus(LogisticsStatusEnum.STATUS_NORMAL.getValue());
        oneBO.setDeliveryStatusList(Collections.singletonList(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue()));
        Logistics logistics = logisticsService.getOneByCondition(oneBO);
        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException("发货单取消领取失败：发货单无效或者已被领取");
        }

        logistics.setOperator("");
        logistics.setDrawTime(null);
        logistics.setUpdatedAt(LocalDateTime.now());
        logisticsService.updateById(logistics);

        // 记录日志
        logisticsLogService.addLog(logistics.getId(),
                LogisticsOperateTypeEnum.TYPE_CANCEL.getCode(),
                "发货单已被" + user.getUsername() + "取消领取",
                user.getUsername());
    }

    public void delivery(LogisticsDeliverDTO deliverDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        LogisticsOneBO oneBO = new LogisticsOneBO();
        oneBO.setId(deliverDTO.getLogisticsId());
        oneBO.setOperator(user.getUsername());
        oneBO.setStatus(LogisticsStatusEnum.STATUS_NORMAL.getValue());
        oneBO.setDeliveryStatusList(Arrays.asList(
                LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue(),
                LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue())
        );
        Logistics logistics = logisticsService.getOneByCondition(oneBO);
        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException("发货单发货失败：发货单不存在或者状态已变更");
        }

        String lockKey = "LogisticsStatusLock:" + logistics.getLogisticsSn();
        try {
            // 发货单操作锁
            if (!ToolsHelper.addLock(redisTemplate, lockKey, 10)) {
                ToolsHelper.throwException("发货单操作中，请稍后！");
            }

            //todo 检查是否能发货

            // 快递单号格式不正确
            String pattern = "^[0-9A-Z]+$";
            if (! Pattern.matches(pattern, deliverDTO.getExpressNumber())) {
                ToolsHelper.throwException("快递单号格式不正确");
            }

            logistics.setExpressNumber(deliverDTO.getExpressNumber());
            logistics.setExpressCorp(deliverDTO.getExpressCorp());
            logistics.setDeliveryRemark(deliverDTO.getDeliveryRemark());
            logistics.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue());
            logistics.setDeliveryTime(LocalDateTime.now());
            logistics.setNotifyStatus(LogisticsNotifyStatusEnum.NOTIFY_STATUS_PROCESSING.getValue());
            logistics.setNotifyTime(null);
            logistics.setNotifyRemark("");
            logistics.setUpdatedAt(LocalDateTime.now());

            logisticsService.updateById(logistics);

            // 记录日志
            logisticsLogService.addLog(logistics.getId(),
                    LogisticsOperateTypeEnum.TYPE_DELIVERY.getCode(),
                    "发货单已被" + user.getUsername() + "发货成功",
                    user.getUsername());

            // 发货通知
            TaskRecordDTO recordDTO = new TaskRecordDTO();
            recordDTO.setReferSn(logistics.getLogisticsSn());
            recordDTO.setReferType(TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP.getType());
            TaskFactory.create(TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP).addAndPush(recordDTO);

        } catch (Throwable e) {
            ToolsHelper.throwException(e.getMessage());
        } finally {
            ToolsHelper.unLock(redisTemplate, lockKey);
        }
    }

    public void stopDelivery(LogisticsStopDeliverDTO stopDeliverDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        LogisticsOneBO oneBO = new LogisticsOneBO();
        oneBO.setId(stopDeliverDTO.getLogisticsId());
        oneBO.setOperator(user.getUsername());
        oneBO.setStatus(LogisticsStatusEnum.STATUS_NORMAL.getValue());
        oneBO.setDeliveryStatusList(Arrays.asList(
                LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue(),
                LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue())
        );
        Logistics logistics = logisticsService.getOneByCondition(oneBO);
        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException("取消发货失败：发货单无效或者已被处理");
        }

        String lockKey = "LogisticsStatusLock:" + logistics.getLogisticsSn();
        try {
            // 发货单操作锁
            if (!ToolsHelper.addLock(redisTemplate, lockKey, 10)) {
                ToolsHelper.throwException("发货单操作中，请稍后！");
            }

            logistics.setStatus(LogisticsStatusEnum.STATUS_STOP.getValue());
            logisticsService.updateById(logistics);

            // 记录日志
            logisticsLogService.addLog(logistics.getId(),
                    LogisticsOperateTypeEnum.TYPE_DELIVERY.getCode(),
                    "发货单已被" + user.getUsername() + "暂停发货",
                    user.getUsername());
        } catch (Throwable e) {
            ToolsHelper.throwException(e.getMessage());
        } finally {
            ToolsHelper.unLock(redisTemplate, lockKey);
        }
    }

    public void resumeDelivery(LogisticsResumeDeliverDTO resumeDeliverDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        LogisticsOneBO oneBO = new LogisticsOneBO();
        oneBO.setId(resumeDeliverDTO.getLogisticsId());
        oneBO.setOperator(user.getUsername());
        oneBO.setStatus(LogisticsStatusEnum.STATUS_STOP.getValue());
        oneBO.setDeliveryStatusList(Arrays.asList(
                LogisticsDeliveryStatusEnum.DELIVERY_STATUS_WAIT.getValue(),
                LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue())
        );
        Logistics logistics = logisticsService.getOneByCondition(oneBO);
        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException("恢复发货失败：发货单无效或者已被处理");
        }

        String lockKey = "LogisticsStatusLock:" + logistics.getLogisticsSn();
        try {
            // 发货单操作锁
            if (!ToolsHelper.addLock(redisTemplate, lockKey, 10)) {
                ToolsHelper.throwException("发货单操作中，请稍后！");
            }

            logistics.setStatus(LogisticsStatusEnum.STATUS_NORMAL.getValue());
            logisticsService.updateById(logistics);

            // 记录日志
            logisticsLogService.addLog(logistics.getId(),
                    LogisticsOperateTypeEnum.TYPE_DELIVERY.getCode(),
                    "发货单已被" + user.getUsername() + "恢复发货",
                    user.getUsername());
        } catch (Throwable e) {
            ToolsHelper.throwException(e.getMessage());
        } finally {
            ToolsHelper.unLock(redisTemplate, lockKey);
        }
    }

    public void notifyResult(LogisticsNotifyDTO notifyDTO) {
        Logistics logistics = logisticsService.getById(notifyDTO.getLogisticsId());
        if (ObjectUtils.isEmpty(logistics)) {
            ToolsHelper.throwException("发货单不存在");
        }

        if (!Arrays.asList(LogisticsNotifyStatusEnum.NOTIFY_STATUS_WAIT.getValue(),
                LogisticsNotifyStatusEnum.NOTIFY_STATUS_PROCESSING.getValue(),
                LogisticsNotifyStatusEnum.NOTIFY_STATUS_FAIL.getValue()).contains(logistics.getNotifyStatus())
        ) {
            ToolsHelper.throwException("重新通知失败：发货单无需通知");
        }

        TaskRecord taskRecord = taskRecordService.getOneByCondition(logistics.getLogisticsSn(), TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP.getType(), null);
        if (ObjectUtils.isEmpty(taskRecord)) {
            TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
            taskRecordDTO.setReferSn(logistics.getLogisticsSn());
            taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP.getType());
            TaskFactory.create(TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP).addAndPush(taskRecordDTO);
        } else {
            TaskFactory.create(TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP).execute(taskRecord.getTaskSn());
        }
    }

    private List<LogisticsGoodsVO> getLogisticsGoodsList(Logistics logistics) {
        // 获取sku发货商品
        List<LogisticsGoodsVO> goodsList = new ArrayList<>();
        if (StringUtils.isNotEmpty(logistics.getGoodsCode())) {
            // 发货单商品
            StorageSkuMap storageSkuMap = storageSkuMapService.getOneByStorageSku(logistics.getStorageCode(), logistics.getGoodsCode());
            if (ObjectUtils.isNotEmpty(storageSkuMap)) {
                LogisticsGoodsVO goods = new LogisticsGoodsVO();
                goods.setGoodsCode(storageSkuMap.getStorageSku());
                goods.setGoodsName(storageSkuMap.getGoodsName());
                goods.setNums(logistics.getNums());
                goodsList.add(goods);
            }
        } else {
            List<LogisticsSku> logisticsSkuList = logisticsSkuService.getListByLogisticsSn(logistics.getLogisticsSn());
            logisticsSkuList.forEach(sku -> {
                LogisticsGoodsVO goods = new LogisticsGoodsVO();
                goods.setGoodsCode(sku.getSku());
                goods.setGoodsName(sku.getGoodsName());
                goods.setNums(sku.getNums());
                goodsList.add(goods);
            });
        }
        return goodsList;
    }

    public Logistics createErpLogisticsOrder(LogisticsErpOrderCreateBO createBO) {
        // 查询是否存在相同的发货单
        LambdaQueryWrapper<Logistics> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Logistics::getExpressNumber, createBO.getExpressNumber())
                .eq(Logistics::getOrderSn, createBO.getOrderSn());

        Logistics logisticsOrder = logisticsService.getOneByWrapper(queryWrapper);
        if (ObjectUtils.isNotEmpty(logisticsOrder)) {
            createBO.setLogisticsSn(logisticsOrder.getLogisticsSn());
            return logisticsOrder;
        }

        // 发货信息检查
        String[] sendAreas = createBO.getSendArea().trim().split("\\s+");
        if(sendAreas.length != 3){
            ToolsHelper.throwException("收件地区格式不正确（省 市 区）");
        }

        // 检查业务sku 与 仓库发货sku是否一致
        Map<String, String> packageSkuMap = new HashMap<>();
        if (ObjectUtils.isNotEmpty(createBO.getGoodsSkuList())) {
            List<String> goodsSkuList = createBO.getGoodsSkuList().stream().map(LogisticsErpOrderCreateBO.Sku::getSku).collect(Collectors.toList());
            List<StorageSkuMap> goodsSkuMapList = storageSkuMapService.getListByStorageCodeSku(createBO.getStorageCode(), goodsSkuList);
            if (ObjectUtils.isEmpty(goodsSkuMapList)) {
                ToolsHelper.throwException("产品包商品sku：" + createBO.getGoodsSkuList() + "仓库sku映射不存在");
            }
            // 仓库发货sku集合
            List<String> erpSkuList = createBO.getSkuList().stream().map(LogisticsErpOrderCreateBO.Sku::getStorageSku).collect(Collectors.toList());
            packageSkuMap = goodsSkuMapList.stream().collect(Collectors.toMap(StorageSkuMap::getStorageSku, StorageSkuMap::getSku));

            goodsSkuMapList.forEach(storageSkuMap -> {
                if (!erpSkuList.contains(storageSkuMap.getStorageSku())) {
                    ToolsHelper.throwException("产品包商品sku：" + storageSkuMap.getSku() + " 仓库sku：" + storageSkuMap.getStorageSku() + "不包含在发货sku列表【" + erpSkuList + "】中");
                }
            });
        }

        // 发货sku信息补充
        List<LogisticsSkuCreateBO> skuCreateBOList = new ArrayList<>();
        Map<String, String> finalPackageSkuMap = packageSkuMap;
        createBO.getSkuList().forEach(sku -> {
            if (ObjectUtils.isEmpty(sku.getStorageSku())) {
                ToolsHelper.throwException("sku编码为空");
            }
            StorageSkuMap skuMap = storageSkuMapService.getOneByStorageSku(createBO.getStorageCode(), sku.getStorageSku());
            if (ObjectUtils.isEmpty(skuMap)) {
                ToolsHelper.throwException("仓库：" + createBO.getStorageCode() + " sku编码：" + sku.getStorageSku() + "不存在");
            }

            LogisticsSkuCreateBO skuCreateBO = new LogisticsSkuCreateBO();
            BeanUtil.copyProperties(sku, skuCreateBO);
            if (StringUtils.isEmpty(skuCreateBO.getSku())) {
                if (ObjectUtils.isNotEmpty(finalPackageSkuMap)) {
                    skuCreateBO.setSku(finalPackageSkuMap.getOrDefault(sku.getStorageSku(), skuMap.getSku()));
                } else {
                    skuCreateBO.setSku(skuMap.getSku());
                }
            }
            if (StringUtils.isEmpty(skuCreateBO.getGoodsName())) {
                skuCreateBO.setGoodsName(skuMap.getGoodsName());
            }
            skuCreateBOList.add(skuCreateBO);
        });

        // 生成发货单
        String logisticsSn = ToolsHelper.genNum(redisTemplate, "etc_logistics", ACTIVE, 8);
        createBO.setLogisticsSn(logisticsSn);
        Logistics logistics = BeanUtil.copyProperties(createBO, Logistics.class);
        logisticsService.create(logistics);

        logisticsSkuService.create(logistics.getLogisticsSn(), skuCreateBOList);

        // 记录日志
        logisticsLogService.addLog(logistics.getId(),
                LogisticsOperateTypeEnum.TYPE_DELIVERY.getCode(),
                "韵达ERP流水号：" + createBO.getErpSn() +
                        " 第三方平台流水号：" + createBO.getOrderSn() +
                        " 物流单号：" + createBO.getExpressNumber(),
                "system");

        return logistics;
    }

    public void createErpExWarehouse(LogisticsErpOrderCreateBO createBO) {
        // 生成出库单
        ExWarehouse exWarehouse = exWarehouseService.getOneByIsvUuid(createBO.getLogisticsSn());
        if (ObjectUtil.isNull(exWarehouse)) {
            String[] sendAreas = createBO.getSendArea().trim().split("\\s+");
            if(sendAreas.length != 3){
                ToolsHelper.throwException("收件地区格式不正确（省 市 区）");
            }
            exWarehouse = new ExWarehouse();
            exWarehouse.setStorageCode(createBO.getStorageCode());
            exWarehouse.setIsvUuid(createBO.getLogisticsSn());
            exWarehouse.setDeliverOrderNo(createBO.getErpSn());
            exWarehouse.setConsigneeName(createBO.getSendName());
            exWarehouse.setConsigneeMobile(createBO.getSendPhone());
            exWarehouse.setConsigneeProvince(sendAreas[0]);
            exWarehouse.setConsigneeCity(sendAreas[1]);
            exWarehouse.setConsigneeArea(sendAreas[2]);
            exWarehouse.setConsigneeAddress(createBO.getSendAddress());
            exWarehouse.setLogisticsCode(createBO.getLogisticsCode());
            exWarehouse.setWayBill(createBO.getExpressNumber());
            exWarehouse.setShipperNo(createBO.getShipperNo());
            exWarehouse.setShipperName(createBO.getExpressCorp());
            exWarehouse.setWarehouseNo(createBO.getWarehouseNo());
            exWarehouse.setCurrentStatus(10020);
            exWarehouse.setRemark(createBO.getRemark());
            exWarehouseService.create(exWarehouse);
        }

        createBO.getSkuList().forEach(sku -> {
            ExWarehouseDetail exWarehouseDetail = exWarehouseDetailService.getOneByIsvUuidAndGoodsNo(
                    createBO.getLogisticsSn(), sku.getStorageSku());

            if (ObjectUtil.isNull(exWarehouseDetail)) {
                exWarehouseDetail = new ExWarehouseDetail();
                exWarehouseDetail.setIsvUuid(createBO.getLogisticsSn());
                exWarehouseDetail.setGoodsNo(sku.getStorageSku());
                exWarehouseDetail.setQuantity(sku.getNums());
                exWarehouseDetail.setCreatedAt(LocalDateTime.now());
                exWarehouseDetail.setUpdatedAt(LocalDateTime.now());
                exWarehouseDetailService.save(exWarehouseDetail);
            } else if(!exWarehouseDetail.getQuantity().equals(sku.getNums())) {
                exWarehouseDetail.setQuantity(sku.getNums());
                exWarehouseDetailService.updateById(exWarehouseDetail);
            }
        });
    }

    public PickUpLogisticsInfoVO getPickUpLogisticsInfo(String plateNo) {
        LogisticsOneBO oneBO = new LogisticsOneBO();
        oneBO.setPlateNo(plateNo);
        oneBO.setDeliveryStatusList(Collections.singletonList(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue()));
        // 查询发货单信息
        Logistics logistics = logisticsService.getOneByCondition(oneBO);
        if (ObjectUtils.isEmpty(logistics)) {
            return null;
        }

        PickUpLogisticsInfoVO infoVO = BeanUtil.copyProperties(logistics, PickUpLogisticsInfoVO.class);
        infoVO.setSendMobile(logistics.getSendPhone());
        // 查询发货sku信息
        List<LogisticsSku> skuList = logisticsSkuService.getListByLogisticsSn(logistics.getLogisticsSn());
        if (ObjectUtils.isNotEmpty(skuList)) {
            skuList.stream()
                    .filter(sku -> ObjectUtils.isNotEmpty(sku.getStorageSku()))
                    .forEach(sku -> {
                        PickupGoodsBO goodsBO = new PickupGoodsBO();
                        goodsBO.setGoodsCode(sku.getStorageSku());
                        goodsBO.setGoodsName(sku.getGoodsName());
                        goodsBO.setQuantity(sku.getNums());
                        // 查询设备类型
                        SupplyGoods goods = supplyGoodsService.getOneByGoodsCode(sku.getStorageSku());
                        if (ObjectUtils.isNotEmpty(goods)) {
                            goodsBO.setDeviceType(goods.getDeviceType());
                        }

                        infoVO.getGoodsList().add(goodsBO);
            });
        }

        return infoVO;
    }
}
