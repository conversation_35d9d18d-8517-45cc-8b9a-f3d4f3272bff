package com.ets.delivery.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.common.annotation.CosSignAnnotation;
import com.ets.delivery.application.app.business.RiskReviewBusiness;
import com.ets.delivery.application.common.dto.riskreview.*;

import com.ets.delivery.application.common.vo.riskreview.AdminRiskReviewsSelectOptionVO;
import com.ets.delivery.application.common.vo.riskreview.RiskReviewDetailVO;
import com.ets.delivery.application.common.vo.riskreview.RiskReviewListVO;
import com.ets.delivery.application.common.vo.riskreview.RiskReviewLogListVO;
import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 风控审核管理
 */
@RestController
@RequestMapping("/admin/risk-reviews")
public class AdminRiskReviewsController {

    @Autowired
    private RiskReviewBusiness riskReviewBusiness;

    /**
     * 获取风控审核列表
     *
     * @param listDTO 列表查询参数
     * @return 风控审核列表
     */
    @PostMapping("/get-list")
    public JsonResult<IPage<RiskReviewListVO>> getList(@RequestBody @Valid RiskReviewListDTO listDTO) {
        return JsonResult.ok(riskReviewBusiness.getList(listDTO));
    }

    /**
     * 获取风控审核详情
     *
     * @param detailDTO 详情查询参数
     * @return 风控审核详情
     */
    @CosSignAnnotation
    @PostMapping("/get-detail")
    public JsonResult<RiskReviewDetailVO> getDetail(@RequestBody @Valid RiskReviewDetailDTO detailDTO) {
        return JsonResult.ok(riskReviewBusiness.getDetail(detailDTO));
    }

    /**
     * 获取风控审核日志列表
     *
     * @param logListDTO 日志查询参数
     * @return 风控审核日志列表
     */
    @PostMapping("/get-log")
    public JsonResult<IPage<RiskReviewLogListVO>> getLog(@RequestBody @Valid RiskReviewLogListDTO logListDTO) {
        return JsonResult.ok(riskReviewBusiness.getLog(logListDTO));
    }

    /**
     * 获取风控审核下拉选项
     *
     * @return 风控审核下拉选项
     */
    @PostMapping("/get-select-option")
    public JsonResult<AdminRiskReviewsSelectOptionVO> getSelectOption() {
        return JsonResult.ok(riskReviewBusiness.getSelectOption());
    }

    /**
     * 获取风控初审数据
     *
     * @param receiveDTO 接收参数
     * @return 风控审核详情
     */
    @CosSignAnnotation
    @PostMapping("/get-preview")
    public JsonResult<RiskReviewDetailVO> getPreview(@RequestBody RiskReviewReceiveDTO receiveDTO) {
        return JsonResult.ok(riskReviewBusiness.receive(receiveDTO));
    }

    /**
     * 提交风控初审结果
     *
     * @param dto 提交参数
     * @return 审核结果，可能包含下一条待审核数据
     */
    @CosSignAnnotation
    @PostMapping("/submit-preview")
    public JsonResult<RiskReviewDetailVO> submitPreview(@RequestBody @Valid RiskReviewSubmitDTO dto) {
        return JsonResult.ok(riskReviewBusiness.submitPreview(dto));
    }

    /**
     * 获取风控复审数据
     *
     * @return 风控审核详情
     */
    @CosSignAnnotation
    @PostMapping("/get-recheck")
    public JsonResult<RiskReviewDetailVO> getRecheck(@RequestBody RiskReviewReceiveDTO receiveDTO) {
        return JsonResult.ok(riskReviewBusiness.receiveRecheck(receiveDTO));
    }

    /**
     * 提交风控复审结果
     *
     * @param dto 提交参数
     * @return 审核结果，可能包含下一条待审核数据
     */
    @CosSignAnnotation
    @PostMapping("/submit-recheck")
    public JsonResult<RiskReviewDetailVO> submitRecheck(@RequestBody @Valid RiskReviewSubmitDTO dto) {
        return JsonResult.ok(riskReviewBusiness.submitRecheck(dto));
    }

}
